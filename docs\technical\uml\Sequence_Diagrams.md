# ClipsMore UML Sequence Diagrams

## Overview
This document provides comprehensive UML sequence diagrams for key use cases and workflows in the ClipsMore application. These diagrams illustrate the interaction flows between different components and the temporal ordering of operations.

## Core Use Cases

### 1. Clip Creation and Storage

```mermaid
sequenceDiagram
    participant User
    participant OS as Operating System
    participant CM as Clipboard Monitor
    participant UI as UI Manager
    participant CTO as ClipsTableOperations
    participant DB as Database
    
    User->>OS: Copy content to clipboard
    OS->>CM: Clipboard content changed event
    CM->>CM: Detect content change
    CM->>CTO: create_clip(content)
    CTO->>DB: INSERT INTO clips_tbl
    DB-->>CTO: Return clip_id
    CTO-->>CM: Clip created successfully
    CM->>UI: Notify new clip available
    UI->>UI: Refresh clips display
    UI->>User: Show new clip in interface
    
    Note over User,DB: Automatic clip creation workflow
```

### 2. Enhanced Clip Assignment Workflow

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Manager
    participant AG as Alias Generator
    participant C<PERSON> as ClipsMoreEnhancedOperations
    participant MTO as MoreTableOperations
    participant DB as Database
    
    User->>UI: Select clip for assignment
    UI->>AG: generate_from_content(clip_content)
    AG->>AG: Extract meaningful words
    AG->>CME: Check alias uniqueness
    CME->>DB: SELECT alias FROM clipsmore_tbl
    DB-->>CME: Return existing aliases
    CME-->>AG: Alias availability status
    AG-->>UI: Generated unique alias
    
    User->>UI: Select business case/component
    UI->>MTO: Validate business case/component
    MTO->>DB: SELECT from more_bus_tbl/more_comp_tbl
    DB-->>MTO: Return validation result
    MTO-->>UI: Validation successful
    
    User->>UI: Click "Assign" button
    UI->>CME: create_assignment(clip_id, bus_id, comp_id, alias)
    CME->>CME: Validate assignment parameters
    CME->>DB: INSERT INTO clipsmore_tbl
    DB-->>CME: Return transaction_id
    CME-->>UI: Assignment created successfully
    UI->>UI: Refresh clips and tree displays
    UI->>User: Show assignment confirmation
    
    Note over User,DB: Complete clip assignment workflow
```

### 3. Drag and Drop Operation

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Manager
    participant DDH as DragDropHandler
    participant CME as ClipsMoreEnhancedOperations
    participant DB as Database
    
    User->>UI: Start dragging clip button
    UI->>DDH: on_drag_start(event)
    DDH->>DDH: Store drag source information
    DDH->>UI: Show drag visual feedback
    
    User->>UI: Drag over potential drop targets
    UI->>DDH: on_drag_motion(event)
    DDH->>DDH: Validate drop target
    DDH->>UI: Update drop zone highlighting
    
    User->>UI: Drop clip button on target
    UI->>DDH: on_drop(event)
    DDH->>DDH: Show context menu (Move/Copy/Cancel)
    DDH->>User: Display context menu
    
    alt User selects "Move"
        User->>DDH: Select "Move" option
        DDH->>CME: move_assignment(transaction_id, target_bus_id, target_comp_id)
        CME->>DB: UPDATE clipsmore_tbl SET more_bus_id, more_comp_id, tree_position
        DB-->>CME: Update successful
        CME-->>DDH: Move completed
        DDH->>UI: Refresh tree display
    else User selects "Copy"
        User->>DDH: Select "Copy" option
        DDH->>CME: copy_assignment(transaction_id, target_bus_id, target_comp_id)
        CME->>CME: Generate new unique alias
        CME->>DB: INSERT INTO clipsmore_tbl (new record)
        DB-->>CME: Return new transaction_id
        CME-->>DDH: Copy completed
        DDH->>UI: Refresh tree display
    else User selects "Cancel"
        User->>DDH: Select "Cancel" option
        DDH->>UI: Reset drag state
        DDH->>UI: Remove visual feedback
    end
    
    DDH->>User: Operation completed
    
    Note over User,DB: Drag and drop with context menu
```

### 4. Business Case and Component Management

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Manager
    participant MTO as MoreTableOperations
    participant CME as ClipsMoreEnhancedOperations
    participant DB as Database
    
    User->>UI: Enter business case name
    User->>UI: Click "Add Business Case"
    UI->>MTO: create_business_case(name)
    MTO->>DB: INSERT INTO more_bus_tbl
    DB-->>MTO: Return more_bus_id
    MTO-->>UI: Business case created
    UI->>UI: Refresh tree display
    
    User->>UI: Select business case
    User->>UI: Enter component name
    User->>UI: Click "CUD Component"
    UI->>MTO: create_component(bus_id, name)
    MTO->>DB: INSERT INTO more_comp_tbl
    DB-->>MTO: Return more_comp_id
    MTO-->>UI: Component created
    UI->>UI: Refresh tree display
    
    Note over User,DB: Business case and component creation
    
    User->>UI: Select component for deletion
    User->>UI: Enter "delete" and click "CUD Component"
    UI->>UI: Confirm deletion with user
    UI->>CME: Check for existing assignments
    CME->>DB: SELECT FROM clipsmore_tbl WHERE more_comp_id = ?
    DB-->>CME: Return assignment count
    
    alt Assignments exist
        CME-->>UI: Assignments found, handle cascade
        UI->>CME: Update assignments (SET more_comp_id = NULL)
        CME->>DB: UPDATE clipsmore_tbl
        DB-->>CME: Assignments updated
    end
    
    UI->>MTO: delete_component(comp_id)
    MTO->>DB: DELETE FROM more_comp_tbl
    DB-->>MTO: Component deleted
    MTO-->>UI: Deletion successful
    UI->>UI: Refresh tree and clips displays
    
    Note over User,DB: Component deletion with referential integrity
```

## Error Handling Sequences

### 5. Database Error Recovery

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Manager
    participant CME as ClipsMoreEnhancedOperations
    participant DB as Database
    participant EM as Error Manager
    
    User->>UI: Attempt clip assignment
    UI->>CME: create_assignment(clip_id, bus_id, comp_id, alias)
    CME->>DB: INSERT INTO clipsmore_tbl
    DB-->>CME: Database error (constraint violation)
    CME->>EM: Handle database error
    
    alt Alias conflict
        EM->>CME: Generate alternative alias
        CME->>CME: Create unique alias variant
        CME->>DB: Retry INSERT with new alias
        DB-->>CME: Success
        CME-->>UI: Assignment created with modified alias
        UI->>User: Show success with alias change notification
    else Foreign key violation
        EM->>UI: Show validation error
        UI->>User: Display error message
        UI->>UI: Reset form state
    else Connection error
        EM->>CME: Attempt connection retry
        CME->>DB: Retry connection
        alt Retry successful
            DB-->>CME: Connection restored
            CME->>DB: Retry original operation
            DB-->>CME: Operation successful
            CME-->>UI: Success after retry
        else Retry failed
            CME-->>UI: Connection failure
            UI->>User: Show connection error dialog
        end
    end
    
    Note over User,EM: Comprehensive error handling and recovery
```

### 6. Migration and Schema Update

```mermaid
sequenceDiagram
    participant App as Application
    participant DM as DatabaseMigrationV2
    participant DB as Database
    participant FS as File System
    participant User
    
    App->>DM: run_migration()
    DM->>FS: backup_database()
    FS->>FS: Create backup file
    FS-->>DM: Backup created successfully
    
    DM->>DB: check_current_schema()
    DB-->>DM: Return schema information
    DM->>DM: Determine migration requirements
    
    alt Migration needed
        DM->>DB: BEGIN TRANSACTION
        DM->>DB: Rename existing table
        DB-->>DM: Table renamed
        DM->>DB: CREATE new table with enhanced schema
        DB-->>DM: New table created
        
        DM->>DB: SELECT existing data
        DB-->>DM: Return existing records
        DM->>DM: Transform data for new schema
        DM->>DM: Generate aliases for existing records
        
        loop For each existing record
            DM->>DB: INSERT transformed record
            DB-->>DM: Record inserted
        end
        
        DM->>DB: DROP old table
        DB-->>DM: Old table dropped
        DM->>DB: CREATE updated view
        DB-->>DM: View created
        DM->>DB: COMMIT TRANSACTION
        DB-->>DM: Migration completed
        
        DM-->>App: Migration successful
        App->>User: Show migration success message
    else No migration needed
        DM-->>App: Database already up to date
        App->>User: Continue normal operation
    end
    
    Note over App,User: Database migration workflow
```

## UI Interaction Sequences

### 7. Theme Switching

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Manager
    participant TM as Theme Manager
    participant Widget as UI Widgets
    
    User->>UI: Click theme toggle button
    UI->>UI: toggle_theme()
    UI->>UI: Update dark_mode flag
    UI->>UI: set_theme()
    UI->>TM: Apply new theme colors
    
    TM->>TM: Calculate color scheme
    TM->>UI: Return theme configuration
    UI->>UI: update_tab_colors()
    
    loop For each tab
        UI->>Widget: Update widget colors
        Widget->>Widget: Apply new background/foreground
        Widget-->>UI: Widget updated
    end
    
    UI->>UI: Update theme button text
    UI->>User: Display updated interface
    
    Note over User,Widget: Theme switching workflow
```

### 8. Tree Refresh and Update

```mermaid
sequenceDiagram
    participant UI as UI Manager
    participant Tree as Treeview Widget
    participant MTO as MoreTableOperations
    participant CME as ClipsMoreEnhancedOperations
    participant DB as Database
    
    UI->>UI: refresh_tree()
    UI->>Tree: Clear existing items
    Tree-->>UI: Tree cleared
    
    UI->>MTO: read_all_business_cases()
    MTO->>DB: SELECT * FROM more_bus_tbl
    DB-->>MTO: Return business cases
    MTO-->>UI: Business cases data
    
    loop For each business case
        UI->>Tree: Insert business case item
        Tree-->>UI: Item inserted
        
        UI->>MTO: read_components_for_business_case(bus_id)
        MTO->>DB: SELECT * FROM more_comp_tbl WHERE more_bus_id = ?
        DB-->>MTO: Return components
        MTO-->>UI: Components data
        
        loop For each component
            UI->>Tree: Insert component item
            Tree-->>UI: Component item inserted
            
            UI->>CME: get_assignments_by_component(comp_id)
            CME->>DB: SELECT * FROM clipsmore_vw WHERE more_comp_id = ?
            DB-->>CME: Return clip assignments
            CME-->>UI: Assignment data
            
            loop For each assignment
                UI->>Tree: Insert clip button
                Tree-->>UI: Clip button inserted
            end
        end
        
        UI->>CME: get_assignments_by_business_case(bus_id)
        CME->>DB: SELECT * FROM clipsmore_vw WHERE more_bus_id = ? AND more_comp_id IS NULL
        DB-->>CME: Return business case assignments
        CME-->>UI: Assignment data
        
        loop For each business case assignment
            UI->>Tree: Insert clip button
            Tree-->>UI: Clip button inserted
        end
    end
    
    UI->>Tree: Expand tree items
    Tree-->>UI: Tree refreshed and displayed
    
    Note over UI,DB: Complete tree refresh with clip buttons
```

## Performance Optimization Sequences

### 9. Lazy Loading Implementation

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Manager
    participant Canvas as Clips Canvas
    participant CTO as ClipsTableOperations
    participant DB as Database
    
    User->>UI: Open clips tab
    UI->>UI: init_clips_tab()
    UI->>CTO: get_clip_count()
    CTO->>DB: SELECT COUNT(*) FROM clips_tbl
    DB-->>CTO: Return total count
    CTO-->>UI: Total clip count
    
    alt Large number of clips (>100)
        UI->>UI: Initialize lazy loading
        UI->>CTO: read_clips_paginated(offset=0, limit=50)
        CTO->>DB: SELECT * FROM clips_tbl LIMIT 50 OFFSET 0
        DB-->>CTO: Return first batch
        CTO-->>UI: First 50 clips
        UI->>Canvas: Create widgets for first batch
        Canvas-->>UI: Widgets created
        
        User->>Canvas: Scroll down
        Canvas->>UI: Scroll event detected
        UI->>UI: Check if more clips needed
        UI->>CTO: read_clips_paginated(offset=50, limit=50)
        CTO->>DB: SELECT * FROM clips_tbl LIMIT 50 OFFSET 50
        DB-->>CTO: Return next batch
        CTO-->>UI: Next 50 clips
        UI->>Canvas: Create widgets for next batch
        Canvas-->>UI: Additional widgets created
    else Small number of clips (≤100)
        UI->>CTO: read_all_clips()
        CTO->>DB: SELECT * FROM clips_tbl
        DB-->>CTO: Return all clips
        CTO-->>UI: All clips data
        UI->>Canvas: Create all widgets at once
        Canvas-->>UI: All widgets created
    end
    
    UI->>User: Display clips interface
    
    Note over User,DB: Lazy loading for performance optimization
```

## Integration Sequences

### 10. Operating System Clipboard Integration

```mermaid
sequenceDiagram
    participant App as Application
    participant CM as Clipboard Monitor
    participant OS as Operating System
    participant CB as System Clipboard
    participant UI as UI Manager
    
    App->>CM: start_monitoring()
    CM->>CM: Initialize monitoring thread
    CM->>OS: Register clipboard change listener
    OS-->>CM: Listener registered
    
    loop Continuous monitoring
        CM->>CB: Check clipboard content
        CB-->>CM: Return current content
        CM->>CM: Compare with last known content
        
        alt Content changed
            CM->>CM: Process new content
            CM->>UI: notify_clipboard_change(content)
            UI->>UI: Create new clip
            UI->>UI: Refresh clips display
        else No change
            CM->>CM: Continue monitoring
        end
        
        CM->>CM: Wait for next check interval
    end
    
    Note over App,UI: Continuous clipboard monitoring
```

This comprehensive set of sequence diagrams covers all major workflows and interactions in the ClipsMore application, providing clear visibility into the temporal aspects of system behavior and component interactions.
