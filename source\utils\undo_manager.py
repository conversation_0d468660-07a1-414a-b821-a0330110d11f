#!/usr/bin/env python3
"""
Undo Manager for ClipsMore Application

This module provides undo functionality for user actions in the application.
It tracks actions and allows users to reverse them using Ctrl+Z or the undo button.
"""

from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class UndoAction:
    """Represents an action that can be undone"""
    action_type: str
    description: str
    undo_function: Callable
    undo_data: Dict[str, Any]
    timestamp: datetime
    
    def __str__(self):
        return f"{self.action_type}: {self.description}"


class UndoManager:
    """
    Manages undo functionality for the application.
    Tracks user actions and provides undo capabilities.
    """
    
    def __init__(self, max_undo_levels: int = 50):
        """
        Initialize undo manager.
        
        Args:
            max_undo_levels: Maximum number of undo actions to keep in history
        """
        print('[DEBUG] UndoManager.__init__ called')
        
        self.max_undo_levels = max_undo_levels
        self.undo_stack: List[UndoAction] = []
        self.enabled = True
        
        print(f'[DEBUG] UndoManager initialized with max_undo_levels={max_undo_levels}')
    
    def add_action(self, action_type: str, description: str, 
                   undo_function: Callable, undo_data: Dict[str, Any]):
        """
        Add an action to the undo stack.
        
        Args:
            action_type: Type of action (e.g., 'delete_clip', 'create_clip', 'edit_alias')
            description: Human-readable description of the action
            undo_function: Function to call to undo this action
            undo_data: Data needed to perform the undo operation
        """
        print(f'[DEBUG] UndoManager.add_action called: {action_type} - {description}')
        
        if not self.enabled:
            print('[DEBUG] UndoManager is disabled, not adding action')
            return
        
        try:
            # Create undo action
            action = UndoAction(
                action_type=action_type,
                description=description,
                undo_function=undo_function,
                undo_data=undo_data,
                timestamp=datetime.now()
            )
            
            # Add to stack
            self.undo_stack.append(action)
            
            # Limit stack size
            if len(self.undo_stack) > self.max_undo_levels:
                removed_action = self.undo_stack.pop(0)
                print(f'[DEBUG] Removed oldest undo action: {removed_action}')
            
            print(f'[DEBUG] Added undo action: {action}. Stack size: {len(self.undo_stack)}')
            
        except Exception as e:
            print(f'[ERROR] Failed to add undo action: {e}')
    
    def undo_last_action(self) -> bool:
        """
        Undo the last action in the stack.
        
        Returns:
            True if an action was undone, False if no actions to undo
        """
        print('[DEBUG] UndoManager.undo_last_action called')
        
        if not self.undo_stack:
            print('[DEBUG] No actions to undo')
            return False
        
        try:
            # Get the last action
            action = self.undo_stack.pop()
            print(f'[DEBUG] Undoing action: {action}')
            
            # Temporarily disable undo tracking to prevent undo operations from being tracked
            self.enabled = False
            
            try:
                # Execute the undo function
                action.undo_function(**action.undo_data)
                print(f'[DEBUG] Successfully undid action: {action.description}')
                return True
                
            finally:
                # Re-enable undo tracking
                self.enabled = True
            
        except Exception as e:
            print(f'[ERROR] Failed to undo action: {e}')
            # Re-add the action to the stack if undo failed
            if 'action' in locals():
                self.undo_stack.append(action)
            return False
    
    def can_undo(self) -> bool:
        """
        Check if there are actions that can be undone.
        
        Returns:
            True if there are actions to undo, False otherwise
        """
        return len(self.undo_stack) > 0
    
    def get_last_action_description(self) -> Optional[str]:
        """
        Get description of the last action that can be undone.
        
        Returns:
            Description of last action, or None if no actions to undo
        """
        if not self.undo_stack:
            return None
        
        return self.undo_stack[-1].description
    
    def clear_history(self):
        """Clear all undo history."""
        print('[DEBUG] UndoManager.clear_history called')
        
        self.undo_stack.clear()
        print('[DEBUG] Undo history cleared')
    
    def get_history_summary(self) -> List[str]:
        """
        Get a summary of all actions in the undo history.
        
        Returns:
            List of action descriptions in chronological order (oldest first)
        """
        return [str(action) for action in self.undo_stack]
    
    def set_enabled(self, enabled: bool):
        """
        Enable or disable undo tracking.
        
        Args:
            enabled: True to enable undo tracking, False to disable
        """
        print(f'[DEBUG] UndoManager.set_enabled called with enabled={enabled}')
        self.enabled = enabled
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current undo manager status.
        
        Returns:
            Dictionary with status information
        """
        return {
            'enabled': self.enabled,
            'stack_size': len(self.undo_stack),
            'max_levels': self.max_undo_levels,
            'can_undo': self.can_undo(),
            'last_action': self.get_last_action_description()
        }
