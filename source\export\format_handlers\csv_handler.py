#!/usr/bin/env python3
"""
CSV Format Handler for ClipsMore Export System
Handles export of clipboard data to CSV format with configurable columns and encoding.
"""

import csv
import os
from datetime import datetime
from typing import Dict, List, Any, Optional


class CSVHandler:
    """
    CSV format handler for exporting ClipsMore data.
    Provides flat structure export with configurable columns and encoding options.
    """
    
    def __init__(self):
        """Initialize the CSV handler."""
        print('[DEBUG] CSVHandler.__init__ called')
        self.format_name = "CSV"
        self.file_extension = ".csv"
        
        # Default column mapping
        self.default_columns = {
            'id': 'ID',
            'clip_id': 'Clip ID',
            'alias': 'Alias',
            'content': 'Content',
            'timestamp': 'Timestamp',
            'business_case': 'Business Case',
            'component': 'Component',
            'business_case_id': 'Business Case ID',
            'component_id': 'Component ID'
        }
    
    def export(self, data: List[Dict[str, Any]], output_path: str, 
               config: Dict[str, Any] = None) -> bool:
        """
        Export data to CSV format.
        
        Args:
            data: List of clip records to export
            output_path: Output file path
            config: Export configuration options
                - columns: List of columns to include (default: all)
                - encoding: File encoding (default: 'utf-8')
                - delimiter: CSV delimiter (default: ',')
                - quoting: CSV quoting style (default: csv.QUOTE_MINIMAL)
                - include_header: Include column headers (default: True)
                - date_format: Date format string (default: ISO format)
                - escape_newlines: Replace newlines in content (default: True)
        
        Returns:
            True if export successful, False otherwise
        """
        print(f'[DEBUG] CSVHandler.export called for {len(data)} records')
        
        try:
            config = config or {}
            
            # Prepare data for CSV export
            csv_data = self._prepare_csv_data(data, config)
            
            # Ensure output path has correct extension
            if not output_path.endswith(self.file_extension):
                output_path += self.file_extension
            
            # Write CSV data to file
            encoding = config.get('encoding', 'utf-8')
            delimiter = config.get('delimiter', ',')
            quoting = config.get('quoting', csv.QUOTE_MINIMAL)
            
            with open(output_path, 'w', newline='', encoding=encoding) as f:
                if csv_data:
                    writer = csv.DictWriter(
                        f,
                        fieldnames=csv_data[0].keys(),
                        delimiter=delimiter,
                        quoting=quoting
                    )
                    
                    if config.get('include_header', True):
                        writer.writeheader()
                    
                    writer.writerows(csv_data)
            
            print(f'[DEBUG] CSV export completed: {output_path}')
            return True
            
        except Exception as e:
            print(f'[ERROR] CSV export failed: {e}')
            return False
    
    def _prepare_csv_data(self, data: List[Dict[str, Any]], 
                         config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare data for CSV export.
        
        Args:
            data: Raw clip data
            config: Export configuration
        
        Returns:
            List of dictionaries ready for CSV export
        """
        print('[DEBUG] CSVHandler._prepare_csv_data called')
        
        csv_rows = []
        selected_columns = config.get('columns', list(self.default_columns.keys()))
        date_format = config.get('date_format', None)
        escape_newlines = config.get('escape_newlines', True)
        
        for record in data:
            row = {}
            
            for column in selected_columns:
                # Get column header name
                header = self.default_columns.get(column, column)
                
                # Get value from record
                value = self._get_column_value(record, column)
                
                # Format value based on type and configuration
                formatted_value = self._format_value(value, column, date_format, escape_newlines)
                
                row[header] = formatted_value
            
            csv_rows.append(row)
        
        return csv_rows
    
    def _get_column_value(self, record: Dict[str, Any], column: str) -> Any:
        """Get value for a specific column from the record."""
        column_mapping = {
            'id': 'transaction_id',
            'clip_id': 'clip_id',
            'alias': 'alias',
            'content': 'content',
            'timestamp': 'timestamp',
            'business_case': 'bus_case',
            'component': 'bus_component',
            'business_case_id': 'more_bus_id',
            'component_id': 'more_comp_id'
        }
        
        field_name = column_mapping.get(column, column)
        return record.get(field_name, '')
    
    def _format_value(self, value: Any, column: str, date_format: Optional[str], 
                     escape_newlines: bool) -> str:
        """Format a value for CSV output."""
        if value is None:
            return ''
        
        # Handle timestamp formatting
        if column == 'timestamp' and value:
            if date_format:
                try:
                    # Assume timestamp is in ISO format, parse and reformat
                    dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    return dt.strftime(date_format)
                except (ValueError, AttributeError):
                    pass
            return str(value)
        
        # Convert to string
        str_value = str(value)
        
        # Escape newlines if requested
        if escape_newlines and column == 'content':
            str_value = str_value.replace('\n', '\\n').replace('\r', '\\r')
        
        return str_value
    
    def get_available_columns(self) -> Dict[str, str]:
        """Get available columns for CSV export."""
        return self.default_columns.copy()
    
    def get_encoding_options(self) -> List[str]:
        """Get available encoding options."""
        return [
            'utf-8',
            'utf-8-sig',  # UTF-8 with BOM for Excel compatibility
            'latin1',
            'cp1252',     # Windows-1252
            'ascii'
        ]
    
    def get_delimiter_options(self) -> Dict[str, str]:
        """Get available delimiter options."""
        return {
            ',': 'Comma (,)',
            ';': 'Semicolon (;)',
            '\t': 'Tab',
            '|': 'Pipe (|)'
        }
    
    def get_quoting_options(self) -> Dict[int, str]:
        """Get available CSV quoting options."""
        return {
            csv.QUOTE_MINIMAL: 'Minimal (only when necessary)',
            csv.QUOTE_ALL: 'All fields',
            csv.QUOTE_NONNUMERIC: 'Non-numeric fields',
            csv.QUOTE_NONE: 'None (use escapechar)'
        }
    
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate CSV export configuration.
        
        Args:
            config: Configuration to validate
        
        Returns:
            List of validation error messages (empty if valid)
        """
        print('[DEBUG] CSVHandler.validate_config called')
        
        errors = []
        
        # Validate columns
        if 'columns' in config:
            available_columns = set(self.default_columns.keys())
            requested_columns = set(config['columns'])
            invalid_columns = requested_columns - available_columns
            
            if invalid_columns:
                errors.append(f"Invalid columns: {', '.join(invalid_columns)}")
        
        # Validate encoding
        if 'encoding' in config:
            try:
                'test'.encode(config['encoding'])
            except LookupError:
                errors.append(f"Invalid encoding: {config['encoding']}")
        
        # Validate delimiter
        if 'delimiter' in config:
            delimiter = config['delimiter']
            if not isinstance(delimiter, str) or len(delimiter) != 1:
                errors.append("Delimiter must be a single character")
        
        # Validate quoting
        if 'quoting' in config:
            valid_quoting = [csv.QUOTE_MINIMAL, csv.QUOTE_ALL, csv.QUOTE_NONNUMERIC, csv.QUOTE_NONE]
            if config['quoting'] not in valid_quoting:
                errors.append("Invalid quoting option")
        
        # Validate date format
        if 'date_format' in config and config['date_format']:
            try:
                datetime.now().strftime(config['date_format'])
            except ValueError:
                errors.append(f"Invalid date format: {config['date_format']}")
        
        return errors
    
    def get_format_info(self) -> Dict[str, Any]:
        """Get information about this format handler."""
        return {
            'name': self.format_name,
            'extension': self.file_extension,
            'description': 'Flat structure export with configurable columns and encoding',
            'features': [
                'Configurable column selection',
                'Multiple encoding options',
                'Customizable delimiters',
                'Date format options',
                'Newline escaping for content',
                'Excel compatibility'
            ],
            'config_options': {
                'columns': 'List of columns to include',
                'encoding': 'File encoding (utf-8, utf-8-sig, latin1, etc.)',
                'delimiter': 'CSV delimiter character',
                'quoting': 'CSV quoting style',
                'include_header': 'Include column headers',
                'date_format': 'Date format string',
                'escape_newlines': 'Replace newlines in content'
            }
        }
