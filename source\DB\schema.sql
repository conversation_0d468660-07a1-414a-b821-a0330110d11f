CREATE TABLE clips_tbl (
        clip_id INTEGER PRIMARY KEY,
        clip BLOB NOT NULL,
        alias TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    );
CREATE TABLE more_bus_tbl (
        more_bus_id INTEGER PRIMARY KEY,
        bus_case TEXT NOT NULL
    );
CREATE TABLE more_comp_tbl (
        more_comp_id INTEGER PRIMARY KEY,
        more_bus_id INTEGER NOT NULL,
        bus_component TEXT,
        FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE
    );
CREATE TABLE clipsmore_tbl (
                            transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                            clip_id INTEGER NOT NULL,
                            alias TEXT UNIQUE,
                            more_bus_id INTEGER NOT NULL,
                            more_comp_id INTEGER,
                            tree_position INTEGER DEFAULT 0,
                            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOR<PERSON><PERSON><PERSON> KEY (clip_id) REFERENCES clips_tbl (clip_id) ON DELETE CASCADE,
                            FOREIGN KEY (more_bus_id) REFERENCES more_bus_tbl (more_bus_id) ON DELETE CASCADE,
                            FOREIGN KEY (more_comp_id) REFERENCES more_comp_tbl (more_comp_id) ON DELETE SET NULL,
                            CONSTRAINT uq_clip_more UNIQUE (clip_id, more_bus_id, more_comp_id)
                        );
CREATE INDEX idx_clipsmore_alias ON clipsmore_tbl(alias);
CREATE INDEX idx_clipsmore_tree_pos ON clipsmore_tbl(tree_position);
CREATE INDEX idx_clipsmore_bus_id ON clipsmore_tbl(more_bus_id);
CREATE INDEX idx_clipsmore_comp_id ON clipsmore_tbl(more_comp_id);
CREATE VIEW clipsmore_vw AS
                    SELECT 
                        cm.transaction_id,
                        cm.clip_id,
                        cm.alias,
                        cm.tree_position,
                        cm.created_date,
                        cm.modified_date,
                        c.clip as clip_content,
                        c.timestamp as clip_timestamp,
                        mb.bus_case as business_case_name,
                        mb.more_bus_id,
                        mc.bus_component as component_name,
                        mc.more_comp_id
                    FROM clipsmore_tbl cm
                    LEFT JOIN clips_tbl c ON cm.clip_id = c.clip_id
                    LEFT JOIN more_bus_tbl mb ON cm.more_bus_id = mb.more_bus_id
                    LEFT JOIN more_comp_tbl mc ON cm.more_comp_id = mc.more_comp_id
                    ORDER BY cm.tree_position, cm.created_date
CREATE INDEX idx_clips_timestamp ON clips_tbl(timestamp DESC);
CREATE INDEX idx_clips_alias ON clips_tbl(alias);
CREATE INDEX idx_more_bus_name ON more_bus_tbl(bus_case);
CREATE INDEX idx_more_comp_bus_id ON more_comp_tbl(more_bus_id);
CREATE INDEX idx_more_comp_name ON more_comp_tbl(bus_component);
CREATE INDEX idx_more_comp_composite ON more_comp_tbl(more_bus_id, bus_component);
CREATE INDEX idx_clipsmore_clip_id ON clipsmore_tbl(clip_id);
CREATE INDEX idx_clipsmore_created ON clipsmore_tbl(created_date DESC);
CREATE INDEX idx_clipsmore_modified ON clipsmore_tbl(modified_date DESC);
CREATE INDEX idx_clipsmore_bus_comp ON clipsmore_tbl(more_bus_id, more_comp_id);
CREATE INDEX idx_clipsmore_bus_tree ON clipsmore_tbl(more_bus_id, tree_position);
CREATE INDEX idx_clipsmore_comp_tree ON clipsmore_tbl(more_comp_id, tree_position);
