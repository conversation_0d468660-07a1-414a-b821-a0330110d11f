import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox

# NOTE: All new code should include debug print statements at the start of every function/method.

from source.utils.scroll_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from source.utils.theme_manager import ThemeManager
from source.utils.utility_manager import UtilityManager
from source.utils.clip_manager import ClipManager
from source.utils.tree_manager import TreeManager
from source.utils.validation_manager import ValidationManager
from source.utils.database_manager import DatabaseManager
from source.utils.event_manager import EventManager
from source.utils.reward_manager import RewardManager
from source.utils.documentation_manager import DocumentationManager
from source.utils.debug_manager import DebugManager
from source.utils.ui_layout_manager import UILayoutManager
from source.utils.drag_drop_manager import DragDropManager
from source.utils.business_logic_manager import BusinessLogicManager
from source.utils.clipboard_monitor import ClipboardMonitor
from source.utils.undo_manager import UndoManager
from source.utils.ui_setup_manager import UISetupManager
from source.utils.tab_initialization_manager import TabInitializationManager
from source.utils.keyboard_manager import KeyboardManager
from source.utils.cleanup_manager import CleanupManager
from source.utils.more_tab_manager import MoreTabManager
from source.utils.reward_system_manager import RewardSystemManager
from source.utils.notification_manager import NotificationManager

class UIManager:
    def __init__(self, root):
        print('[DEBUG] UIManager.__init__ called')
        self.root = root

        # Initialize new utility managers
        self.ui_setup_manager = UISetupManager(root)
        self.keyboard_manager = KeyboardManager(root)
        self.cleanup_manager = CleanupManager()

        # Initialize core managers using setup manager
        self.theme_manager = ThemeManager(root)
        self.database_manager = DatabaseManager()
        self.validation_manager = ValidationManager(self.database_manager)

        # Initialize notification manager early so it's available to other managers
        self.notification_manager = NotificationManager(root, self.theme_manager)

        # Register core managers with setup manager
        self.ui_setup_manager.initialize_core_managers(
            self.theme_manager,
            self.database_manager,
            self.validation_manager
        )

        # Initialize UI layout manager
        self.ui_layout_manager = UILayoutManager(root, self.theme_manager)
        layout_components = self.ui_setup_manager.initialize_layout_manager(self.ui_layout_manager)
        self.main_frame = layout_components['main_frame']
        self.top_frame = layout_components['top_frame']

        # Create control buttons using setup manager
        self.theme_button, self.undo_button = self.ui_setup_manager.create_control_buttons(
            self.ui_layout_manager,
            self.toggle_theme,
            self.undo_last_action
        )

        # Add top-level debug button
        self._add_top_level_debug_button()

        # Create tab control using setup manager
        self.tab_control, tabs = self.ui_setup_manager.create_tab_control(self.ui_layout_manager)
        self.clips_tab = tabs['clips_tab']
        self.more_tab = tabs['more_tab']
        self.about_tab = tabs['about_tab']

        # Setup responsive layout using setup manager
        self.ui_setup_manager.setup_responsive_layout(self.ui_layout_manager)

        # Initialize managers that will be set later
        self.reward_manager = RewardManager(root)
        self.reward_system_manager = None  # Will be initialized after tabs are created
        self.event_manager = None
        self.documentation_manager = None
        self.debug_manager = None
        self.drag_drop_manager = None
        self.business_logic_manager = None
        self.clipboard_monitor = None

        # Initialize export and backup managers
        self.export_manager = None
        self.backup_manager = None

        # Initialize undo manager using setup manager
        self.undo_manager = self.ui_setup_manager.initialize_undo_manager(UndoManager)

        # Initialize scroll handler using setup manager
        self.scroll_handler = self.ui_setup_manager.initialize_scroll_handler(ScrollHandler, self.tab_control)
        self.scroll_handler.set_ui_manager(self)

        # Get theme colors for backward compatibility
        self._update_theme_properties()

        # Initialize tab content using new tab initialization manager
        self.tab_initialization_manager = TabInitializationManager(
            self.theme_manager,
            self.database_manager,
            self.validation_manager
        )

        self._initialize_tabs()
        self._initialize_post_tab_managers()
        self._setup_application_bindings()
        self._register_cleanup_handlers()

    def _initialize_tabs(self):
        """Initialize all tab content using TabInitializationManager"""
        print('[DEBUG] UIManager._initialize_tabs called')

        try:
            # Initialize clips tab
            self.clip_manager = self.tab_initialization_manager.initialize_clips_tab(
                self.clips_tab, ClipManager, self
            )
            # Pass notification manager to clip manager
            if hasattr(self.clip_manager, 'set_notification_manager'):
                self.clip_manager.set_notification_manager(self.notification_manager)

            # Initialize about tab
            self.documentation_manager = self.tab_initialization_manager.initialize_about_tab(
                self.about_tab, DocumentationManager
            )

            # Initialize more tab using MoreTabManager
            self.more_tab_manager = MoreTabManager(self.theme_manager, self.database_manager)
            more_tab_components = self.more_tab_manager.initialize_more_tab(
                self.more_tab, TreeManager, DragDropManager, BusinessLogicManager, self
            )

            # Extract managers from more tab components
            self.tree_manager = more_tab_components['tree_manager']
            self.drag_drop_manager = more_tab_components['drag_drop_manager']
            self.business_logic_manager = more_tab_components['business_logic_manager']
            self.tree = more_tab_components['tree']

            # Pass notification manager to business logic manager
            if hasattr(self.business_logic_manager, 'set_notification_manager'):
                self.business_logic_manager.set_notification_manager(self.notification_manager)

            # Store UI elements for backward compatibility
            more_ui_elements = more_tab_components['ui_elements']
            self.bus_entry_var = more_ui_elements['bus_entry_var']
            self.comp_entry_var = more_ui_elements['comp_entry_var']
            self.more_error_label = more_ui_elements['more_error_label']
            self.add_component_btn = more_ui_elements['add_component_btn']
            self.search_var = more_ui_elements['search_var']

            # Initialize reward system manager for the more tab
            self.reward_system_manager = RewardSystemManager(self.more_tab)

            print('[DEBUG] All tabs initialized successfully')

        except Exception as e:
            print(f'[ERROR] Failed to initialize tabs: {e}')
            raise

    def _initialize_post_tab_managers(self):
        """Initialize managers that depend on tab content being created"""
        print('[DEBUG] UIManager._initialize_post_tab_managers called')

        try:
            # Bind tree selection event
            if hasattr(self, 'tree') and self.tree:
                self.tree.bind('<<TreeviewSelect>>', self._on_treeview_select)

            # Initialize EventManager after UI components are created
            self._initialize_event_manager()

            # Initialize DebugManager after all other managers are ready
            self._initialize_debug_manager()

            # Initialize and start clipboard monitoring
            self._initialize_clipboard_monitor()

            print('[DEBUG] Post-tab managers initialized successfully')

        except Exception as e:
            print(f'[ERROR] Failed to initialize post-tab managers: {e}')

    def _setup_application_bindings(self):
        """Setup application-wide keyboard bindings using KeyboardManager"""
        print('[DEBUG] UIManager._setup_application_bindings called')

        try:
            self.keyboard_manager.setup_global_bindings(self.undo_last_action)
            print('[DEBUG] Application bindings setup successfully')

        except Exception as e:
            print(f'[ERROR] Failed to setup application bindings: {e}')

    def _register_cleanup_handlers(self):
        """Register cleanup handlers with CleanupManager"""
        print('[DEBUG] UIManager._register_cleanup_handlers called')

        try:
            # Register clipboard monitor cleanup
            self.cleanup_manager.register_cleanup_handler(
                lambda: self.cleanup_manager.cleanup_clipboard_monitor(self.clipboard_monitor),
                'clipboard_monitor_cleanup'
            )

            # Register reward system manager cleanup
            if self.reward_system_manager:
                self.cleanup_manager.register_manager_for_cleanup(
                    self.reward_system_manager, 'reward_system_manager'
                )

            print('[DEBUG] Cleanup handlers registered successfully')

        except Exception as e:
            print(f'[ERROR] Failed to register cleanup handlers: {e}')

    def _update_theme_properties(self):
        """Update theme properties from theme manager for backward compatibility"""
        print('[DEBUG] UIManager._update_theme_properties called')
        colors = self.theme_manager.get_theme_colors()
        self.dark_mode = colors['dark_mode']
        self.bg_color = colors['bg_color']
        self.fg_color = colors['fg_color']
        self.entry_bg = colors['entry_bg']
        self.entry_fg = colors['entry_fg']
        self.button_bg = colors['button_bg']
        self.button_fg = colors['button_fg']
        self.tree_bg = colors['tree_bg']
        self.tree_fg = colors['tree_fg']
        self.tree_select = colors['tree_select']

    def _initialize_event_manager(self):
        """Initialize EventManager with UI component references"""
        print('[DEBUG] UIManager._initialize_event_manager called')

        try:
            # Prepare UI components dictionary for EventManager
            ui_components = {
                'tree': getattr(self, 'tree', None),
                'tree_manager': self.tree_manager,
                'clip_manager': self.clip_manager,
                'database_manager': self.database_manager,
                'theme_manager': self.theme_manager,
                'validation_manager': self.validation_manager
            }

            # Initialize EventManager with reference to UIManager
            self.event_manager = EventManager(ui_components, ui_manager=self)

            # Bind events if tree is available
            if hasattr(self, 'tree') and self.tree:
                self.event_manager.bind_events()
                print('[DEBUG] EventManager events bound successfully')
            else:
                print('[WARNING] Tree not available for event binding')

        except Exception as e:
            print(f'[ERROR] Failed to initialize EventManager: {e}')

    def _initialize_debug_manager(self):
        """Initialize DebugManager with manager references"""
        print('[DEBUG] UIManager._initialize_debug_manager called')

        try:
            # Initialize DebugManager with required manager references
            self.debug_manager = DebugManager(
                self.database_manager,
                self.clip_manager,
                self.tree_manager
            )

            # Pass notification manager to debug manager
            if hasattr(self.debug_manager, 'set_notification_manager'):
                self.debug_manager.set_notification_manager(self.notification_manager)

            print('[DEBUG] DebugManager initialized successfully')

        except Exception as e:
            print(f'[ERROR] Failed to initialize DebugManager: {e}')

    def _initialize_clipboard_monitor(self):
        """Initialize and start clipboard monitoring"""
        print('[DEBUG] UIManager._initialize_clipboard_monitor called')

        try:
            # Initialize clipboard monitor with callback to refresh clips
            self.clipboard_monitor = ClipboardMonitor(
                root=self.root,
                database_manager=self.database_manager,
                callback=self._on_new_clip_detected,
                check_interval=1.0  # Check every second
            )

            # Start monitoring
            self.clipboard_monitor.start_monitoring()
            print('[DEBUG] Clipboard monitoring initialized and started')

        except Exception as e:
            print(f'[ERROR] Failed to initialize clipboard monitor: {e}')

    def _on_new_clip_detected(self, content: str):
        """Callback function called when new clipboard content is detected"""
        print(f'[DEBUG] UIManager._on_new_clip_detected called with {len(content)} characters')

        try:
            # Refresh clips display to show new clip
            if self.clip_manager:
                # Use after_idle to ensure UI update happens on main thread
                self.root.after_idle(self.clip_manager.load_clips)
                print('[DEBUG] Scheduled clips refresh after new clip detection')
            else:
                print('[WARNING] ClipManager not available for refresh')

        except Exception as e:
            print(f'[ERROR] Error handling new clip detection: {e}')

    def _add_top_level_debug_button(self):
        """Add a top-level debug button for loading all test data."""
        print('[DEBUG] UIManager._add_top_level_debug_button called')

        try:
            import tkinter as tk

            # Create debug frame in the top frame
            debug_frame = tk.Frame(self.top_frame, bg=self.theme_manager.bg_color)
            debug_frame.pack(side=tk.RIGHT, padx=10, pady=5)

            # Create the debug button
            debug_button = tk.Button(
                debug_frame,
                text="🔧 DEBUG: Load All Test Data",
                command=self._debug_load_all_test_data,
                bg="#ff9800",
                fg="white",
                activebackground="#f57c00",
                activeforeground="white",
                font=('Arial', 10, 'bold'),
                relief=tk.RAISED,
                bd=2
            )
            debug_button.pack(side=tk.RIGHT, padx=5)

            print('[DEBUG] Top-level debug button added successfully')

        except Exception as e:
            print(f'[ERROR] Failed to add top-level debug button: {e}')

    def _debug_load_all_test_data(self):
        """Load all cybersecurity test data (clips, business cases, components, and transactions)."""
        print('[DEBUG] UIManager._debug_load_all_test_data called')

        if self.debug_manager:
            self.debug_manager.load_test_clips()  # This now loads everything
        else:
            print('[WARNING] DebugManager not initialized')

    def undo_last_action(self):
        """Undo the last user action"""
        print('[DEBUG] UIManager.undo_last_action called')

        try:
            if self.undo_manager.undo_last_action():
                # Update undo button state
                self._update_undo_button_state()

                # Show feedback to user
                last_action = self.undo_manager.get_last_action_description()
                if last_action:
                    print(f'[DEBUG] Action undone successfully. Next undoable action: {last_action}')
                else:
                    print('[DEBUG] Action undone successfully. No more actions to undo.')

                # Refresh UI components that might have changed
                self._refresh_after_undo()

            else:
                print('[DEBUG] No actions to undo')

        except Exception as e:
            print(f'[ERROR] Failed to undo action: {e}')

    def _update_undo_button_state(self):
        """Update the undo button enabled/disabled state"""
        print('[DEBUG] UIManager._update_undo_button_state called')

        try:
            if hasattr(self, 'undo_button') and self.undo_button:
                if self.undo_manager.can_undo():
                    self.undo_button.config(state=tk.NORMAL)
                    last_action = self.undo_manager.get_last_action_description()
                    if last_action:
                        self.undo_button.config(text=f"↶ Undo: {last_action[:20]}...")
                    else:
                        self.undo_button.config(text="↶ Undo (Ctrl+Z)")
                else:
                    self.undo_button.config(state=tk.DISABLED)
                    self.undo_button.config(text="↶ Undo (Ctrl+Z)")

        except Exception as e:
            print(f'[ERROR] Failed to update undo button state: {e}')

    def _refresh_after_undo(self):
        """Refresh UI components after an undo operation"""
        print('[DEBUG] UIManager._refresh_after_undo called')

        try:
            # Refresh clips display
            if hasattr(self, 'clip_manager') and self.clip_manager:
                self.clip_manager.load_clips()

            # Refresh tree display
            if hasattr(self, 'tree_manager') and self.tree_manager:
                self.tree_manager.refresh_tree()

        except Exception as e:
            print(f'[ERROR] Failed to refresh UI after undo: {e}')

    def cleanup(self):
        """Cleanup resources when application is closing using CleanupManager"""
        print('[DEBUG] UIManager.cleanup called')

        try:
            # Execute all registered cleanup operations
            self.cleanup_manager.execute_cleanup(self.root)
            print('[DEBUG] Cleanup completed successfully')

        except Exception as e:
            print(f'[ERROR] Error during cleanup: {e}')

    def toggle_theme(self):
        print('[DEBUG] UIManager.toggle_theme called')
        """Toggle between light and dark mode using ThemeManager and UILayoutManager"""
        self.dark_mode = self.theme_manager.toggle_theme()
        self._update_theme_properties()

        # Apply theme to layout using UILayoutManager
        theme_colors = self.theme_manager.get_theme_colors()
        self.ui_layout_manager.apply_layout_theme(theme_colors)

        # Update DragDropManager theme if available
        if self.drag_drop_manager:
            self.drag_drop_manager.update_theme()

        # Update NotificationManager theme
        if self.notification_manager:
            self.notification_manager.update_theme()

        self.update_tab_colors()

    def update_tab_colors(self):
        print('[DEBUG] UIManager.update_tab_colors called')
        """Update colors for all tab content using UILayoutManager"""
        tabs = {
            'clips_tab': self.clips_tab,
            'more_tab': self.more_tab,
            'about_tab': self.about_tab
        }
        self.ui_layout_manager.update_tab_colors(tabs)

    def load_enhanced_clips(self):
        print('[DEBUG] UIManager.load_enhanced_clips called')
        """Load and display clips using ClipManager"""
        if self.clip_manager:
            self.clip_manager.load_clips()
        else:
            print('[WARNING] ClipManager not initialized')

    def _limit_entry(self, var, maxlen):
        print('[DEBUG] UIManager._limit_entry called')
        UtilityManager.limit_entry_length(var, maxlen)

    # Legacy alias button methods moved to ClipManager

    def add_business_case(self):
        """Add business case using BusinessLogicManager"""
        print('[DEBUG] UIManager.add_business_case called')

        if self.business_logic_manager:
            self.business_logic_manager.add_business_case()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def cud_component(self):
        """Handle component CUD operations using BusinessLogicManager"""
        print('[DEBUG] UIManager.cud_component called')

        if self.business_logic_manager:
            self.business_logic_manager.handle_component_operations()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def open_export_dialog(self):
        """Open the export dialog"""
        print('[DEBUG] UIManager.open_export_dialog called')

        try:
            from export.export_dialog import ExportDialog

            dialog = ExportDialog(
                parent=self.root,
                database_manager=self.database_manager,
                theme_manager=self.theme_manager
            )

        except Exception as e:
            print(f'[ERROR] Failed to open export dialog: {e}')
            if hasattr(self, 'notification_manager'):
                self.notification_manager.show_error(f"Export Error: {e}")

    def open_backup_dialog(self):
        """Open the backup dialog"""
        print('[DEBUG] UIManager.open_backup_dialog called')

        try:
            from backup.backup_dialog import BackupDialog

            dialog = BackupDialog(
                parent=self.root,
                database_manager=self.database_manager,
                theme_manager=self.theme_manager
            )

        except Exception as e:
            print(f'[ERROR] Failed to open backup dialog: {e}')
            if hasattr(self, 'notification_manager'):
                self.notification_manager.show_error(f"Backup Error: {e}")

    def edit_selected(self):
        """Edit selected item using BusinessLogicManager"""
        print('[DEBUG] UIManager.edit_selected called')

        if self.business_logic_manager:
            self.business_logic_manager.edit_selected_item()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def delete_selected(self):
        """Delete selected item using BusinessLogicManager"""
        print('[DEBUG] UIManager.delete_selected called')

        if self.business_logic_manager:
            self.business_logic_manager.delete_selected_item()
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def refresh_tree(self):
        print('[DEBUG] UIManager.refresh_tree called')
        """Refresh tree using TreeManager"""
        if self.tree_manager:
            self.tree_manager.refresh_tree()
        else:
            print('[WARNING] TreeManager not initialized')

    def _create_clip_buttons_for_item(self, item_name, assignments, y_position):
        """Create clip buttons for a business case or component"""
        print(f'[DEBUG] UIManager._create_clip_buttons_for_item called for {item_name} with {len(assignments)} assignments')

        # Create a label for the item name
        item_label = tk.Label(self.buttons_scrollable_frame,
                             text=f"{item_name}:",
                             bg=self.bg_color, fg=self.fg_color,
                             font=('Arial', 10, 'bold'),
                             anchor='w')
        item_label.grid(row=y_position, column=0, sticky='w', padx=5, pady=(10, 2))
        y_position += 1

        # Create frame for buttons in this row
        buttons_frame = tk.Frame(self.buttons_scrollable_frame, bg=self.bg_color)
        buttons_frame.grid(row=y_position, column=0, sticky='w', padx=20, pady=2)

        # Create buttons for each assignment
        for i, assignment in enumerate(assignments):
            alias = assignment.get('alias', 'Unknown')

            # Calculate button width based on alias length (minimum 8, maximum 20)
            button_width = max(8, min(20, len(alias) + 2))

            # Create the clip button
            clip_button = tk.Button(buttons_frame,
                                  text=alias,
                                  command=lambda a=alias: self._copy_clip_by_alias(a),
                                  bg="#4CAF50", fg="white",
                                  activebackground="#45a049", activeforeground="white",
                                  font=('Arial', 9, 'bold'),
                                  width=button_width,
                                  relief='raised',
                                  bd=2,
                                  cursor='hand2')

            # Pack buttons close together, aligned left
            clip_button.pack(side=tk.LEFT, padx=2, pady=1)

            # Store button reference for management
            button_key = f"{item_name}_{alias}"
            self.clip_button_widgets[button_key] = clip_button

            print(f'[DEBUG] Created clip button: {alias} for {item_name}')

        return y_position + 1

    def _show_emoji_reward(self):
        """Show a fun emoji reward popup using RewardSystemManager"""
        print('[DEBUG] UIManager._show_emoji_reward called')

        if self.reward_system_manager:
            self.reward_system_manager.show_emoji_reward()
        else:
            print('[WARNING] RewardSystemManager not initialized')

    def _hide_emoji_reward(self):
        """Hide the emoji reward popup using RewardSystemManager"""
        print('[DEBUG] UIManager._hide_emoji_reward called')

        if self.reward_system_manager:
            self.reward_system_manager.hide_emoji_reward()
        else:
            print('[WARNING] RewardSystemManager not initialized')

    def filter_tree(self):
        print('[DEBUG] UIManager.filter_tree called')
        """Filter treeview using TreeManager"""
        if self.tree_manager and hasattr(self, 'search_var'):
            query = self.search_var.get()
            self.tree_manager.filter_tree(query)
        else:
            print('[WARNING] TreeManager not initialized or search_var not available')

    def on_tree_double_click(self, event):
        """Handle double-click on tree items, especially clip buttons"""
        print('[DEBUG] UIManager.on_tree_double_click called')

        item = self.tree.identify_row(event.y)
        if not item:
            return

        item_values = self.tree.item(item, 'values')
        item_text = self.tree.item(item, 'text')

        # Check if it's a clip button
        if item_values and item_values[0] == 'Clip Button':
            # Extract alias from the text (remove the 📎 emoji)
            alias = item_text.replace('📎 ', '').strip()
            self._copy_clip_by_alias(alias)

    def _copy_clip_by_alias(self, alias):
        """Copy clip content to clipboard by alias"""
        print(f'[DEBUG] UIManager._copy_clip_by_alias called with alias={alias}')

        try:
            import sqlite3

            # Get clip content by alias using the view
            with sqlite3.connect("source/DB/clipsmore_db.db") as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT clip_content
                    FROM clipsmore_vw
                    WHERE alias = ?
                    LIMIT 1
                """, (alias,))

                result = cursor.fetchone()
                if result:
                    content = result[0]
                    if content:
                        self.root.clipboard_clear()
                        self.root.clipboard_append(content)
                        print(f'[DEBUG] Copied content for alias: {alias}')

                        # Show brief feedback
                        if self.notification_manager:
                            self.notification_manager.show_success(f"Content for '{alias}' copied to clipboard!")
                        else:
                            from tkinter import messagebox
                            messagebox.showinfo("Copied", f"Content for '{alias}' copied to clipboard!")
                    else:
                        print(f'[WARNING] No content found for alias: {alias}')
                else:
                    print(f'[WARNING] Alias not found: {alias}')

        except Exception as e:
            print(f'[ERROR] Failed to copy clip by alias: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to copy clip: {e}")
            else:
                from tkinter import messagebox
                messagebox.showerror("Error", f"Failed to copy clip: {e}")

    def on_tree_item_press(self, event):
        """Handle mouse press on tree item for drag initiation using DragDropManager"""
        print('[DEBUG] UIManager.on_tree_item_press called')

        if self.drag_drop_manager:
            self.drag_drop_manager.handle_drag_start(event)

    def on_tree_item_drag(self, event):
        """Handle drag motion using DragDropManager"""
        if self.drag_drop_manager:
            self.drag_drop_manager.handle_drag_motion(event)

    def on_tree_select(self, event):
        """Handle tree selection event"""
        print('[DEBUG] UIManager.on_tree_select called')
        
        if self.business_logic_manager:
            self.business_logic_manager.handle_tree_item_selection(event)
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def on_bus_entry_change(self, event=None):
        """Handle business case entry change using BusinessLogicManager"""
        print('[DEBUG] UIManager.on_bus_entry_change called')

        if self.business_logic_manager:
            self.business_logic_manager.handle_business_case_entry_change(event)
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def on_comp_entry_change(self, event=None):
        """Handle component entry change using BusinessLogicManager"""
        print('[DEBUG] UIManager.on_comp_entry_change called')

        if self.business_logic_manager:
            self.business_logic_manager.handle_component_entry_change(event)
        else:
            print('[WARNING] BusinessLogicManager not initialized')

    def create_tooltip(self, widget, text):
        """Create a tooltip for a widget using UtilityManager"""
        UtilityManager.create_tooltip(widget, text, self.theme_manager)

    def _validate_alias_realtime(self, alias, validation_label):
        """Validate alias in real-time using ValidationManager"""
        if self.validation_manager:
            self.validation_manager.validate_alias_realtime(alias, validation_label)
        else:
            print('[WARNING] ValidationManager not initialized')
            validation_label.config(text="?", fg="gray")

    def _copy_clip_to_clipboard(self, clip_id):
        """Copy clip content to OS clipboard using ClipManager"""
        print(f'[DEBUG] UIManager._copy_clip_to_clipboard called for clip_id={clip_id}')
        if self.clip_manager:
            self.clip_manager.copy_clip_to_clipboard(clip_id)
        else:
            print('[WARNING] ClipManager not initialized')

    def _get_or_generate_alias(self, clip_id, clip_content):
        """Get existing alias or generate new one for clip using DatabaseManager"""
        print(f'[DEBUG] UIManager._get_or_generate_alias called for clip_id={clip_id}')

        try:
            from utils.alias_generator import AliasGenerator

            # Get existing aliases for uniqueness checking
            existing_aliases = self.database_manager.get_existing_aliases()

            # Initialize alias generator
            generator = AliasGenerator()
            generator.set_existing_aliases(existing_aliases)

            # Generate intelligent alias
            alias = generator.generate_from_content(str(clip_content) if clip_content else "")

            print(f'[DEBUG] Generated alias: {alias}')
            return alias

        except Exception as e:
            print(f'[ERROR] Failed to generate alias: {e}')
            return f"clip_{clip_id}"

    def _populate_assignment_dropdown(self, combobox, clip_id=None):
        """Populate assignment dropdown with business cases and components using DatabaseManager"""
        print(f'[DEBUG] UIManager._populate_assignment_dropdown called for clip_id={clip_id}')

        try:
            more_ops = self.database_manager.get_more_operations()
            options = ["None"]  # Option for no assignment
            max_length = len("None")
            current_assignment = "None"  # Default to None

            # Get current assignment for this clip if clip_id is provided
            if clip_id:
                try:
                    enhanced_ops = self.database_manager.get_enhanced_operations()
                    assignments = enhanced_ops.get_assignments_by_clip(clip_id)
                    if assignments:
                        # Get the most recent assignment
                        assignment = assignments[0]
                        bc_name = assignment.get('business_case_name', '')
                        comp_name = assignment.get('component_name', '')

                        if comp_name:
                            current_assignment = f"BC: {bc_name} > {comp_name}"
                        elif bc_name:
                            current_assignment = f"BC: {bc_name}"

                        print(f'[DEBUG] Found current assignment: {current_assignment}')
                except Exception as e:
                    print(f'[WARNING] Failed to get current assignment for clip {clip_id}: {e}')

            # Get business cases
            business_cases = more_ops.read_all_business_cases()
            for bc in business_cases:
                bc_name = bc.get('name', '')
                bc_option = f"BC: {bc_name}"
                options.append(bc_option)
                max_length = max(max_length, len(bc_option))

                # Get components for this business case
                bc_id = bc.get('id')
                components = more_ops.read_components_for_business_case(bc_id)
                for comp in components:
                    comp_name = comp.get('name', '')
                    comp_option = f"BC: {bc_name} > {comp_name}"
                    options.append(comp_option)
                    max_length = max(max_length, len(comp_option))

            # Set combobox values and current selection
            combobox['values'] = options
            combobox.set(current_assignment)  # Set to current assignment or "None"
            UtilityManager.auto_size_dropdown(combobox, options)

            print(f'[DEBUG] Dropdown populated with {len(options)} options, current: {current_assignment}')

        except Exception as e:
            print(f'[ERROR] Failed to populate dropdown: {e}')
            combobox['values'] = ["None"]
            combobox.set("None")

    # Assignment logic moved to ClipManager

    def _delete_clip(self, clip_id):
        """Delete clip using ClipManager"""
        print(f'[DEBUG] UIManager._delete_clip called for clip_id={clip_id}')
        if self.clip_manager:
            self.clip_manager.delete_clip(clip_id)
        else:
            print('[WARNING] ClipManager not initialized')

    def clear_all_clips(self):
        """Clear all clips using ClipManager"""
        print('[DEBUG] UIManager.clear_all_clips called')
        if self.clip_manager:
            self.clip_manager.clear_all_clips()
        else:
            print('[WARNING] ClipManager not initialized')

    def clear_all_more_data(self):
        """Clear all business cases, components, and assignments using DebugManager"""
        print('[DEBUG] UIManager.clear_all_more_data called')
        if self.debug_manager:
            self.debug_manager.clear_all_more_data()
        else:
            print('[WARNING] DebugManager not initialized')



    def test_notifications(self):
        """Test all notification types using DebugManager"""
        print('[DEBUG] UIManager.test_notifications called')
        if self.debug_manager:
            self.debug_manager.test_notifications()
        else:
            print('[WARNING] DebugManager not initialized')

    def on_tree_selection_changed(self, item, item_type, name):
        print(f'[DEBUG] UIManager.on_tree_selection_changed called: item={item}, type={item_type}, name={name}')
        # Only populate business case field when business case is selected
        # Component field should remain empty for user input unless explicitly populated
        if item_type == 'Business Case':
            self.bus_entry_var.set(name)
            # Clear component field when business case is selected to allow new component creation
            self.comp_entry_var.set('')
        elif item_type == 'Component':
            # Set parent business case name but DO NOT auto-populate component field
            # This allows the user to type a new name for create/update operations
            parent_item = self.tree.parent(item)
            if parent_item:
                parent_name = self.tree.item(parent_item, 'text')
                self.bus_entry_var.set(parent_name)
            # Component field remains unchanged - user must manually enter name for operations

    def _on_treeview_select(self, event):
        selected = self.tree.selection()
        if selected:
            item = selected[0]
            values = self.tree.item(item, 'values')
            item_type = values[0] if values and len(values) > 0 else None
            name = self.tree.item(item, 'text')
            self.on_tree_selection_changed(item, item_type, name)
