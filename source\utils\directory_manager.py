#!/usr/bin/env python3
"""
Directory Manager for ClipsMore
Handles smart directory detection with OneDrive migration support.
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional


class DirectoryManager:
    """
    Smart directory manager that handles OneDrive-migrated user profiles.
    Detects correct Desktop, Documents, and Downloads paths even when
    user profiles have been migrated by OneDrive to non-standard locations.
    """
    
    def __init__(self):
        """Initialize the directory manager."""
        print('[DEBUG] DirectoryManager.__init__ called')
        self.current_working_dir = os.getcwd()
        self.user_directories = None
        self._detect_user_directories()
    
    def _detect_user_directories(self):
        """Detect user directories with OneDrive migration support."""
        print('[DEBUG] DirectoryManager._detect_user_directories called')
        
        # Start from current working directory to handle OneDrive migrations
        base_path = self.detect_user_profile_base()
        
        self.user_directories = {
            'desktop': self.find_desktop_directory(base_path),
            'documents': self.find_documents_directory(base_path),
            'downloads': self.find_downloads_directory(base_path),
            'profile': base_path
        }
        
        print(f'[DEBUG] Detected user directories: {self.user_directories}')
    
    def detect_user_profile_base(self) -> Path:
        """
        Detect user profile base handling OneDrive migrations.
        
        Returns:
            Path to the user profile base directory
        """
        print(f'[DEBUG] DirectoryManager.detect_user_profile_base called from {self.current_working_dir}')
        
        # Check if current working directory indicates OneDrive migration
        cwd_path = Path(self.current_working_dir)
        cwd_parts = cwd_path.parts
        
        print(f'[DEBUG] Current working directory parts: {cwd_parts}')
        
        # Look for OneDrive indicators in path
        for i, part in enumerate(cwd_parts):
            if 'OneDrive' in part:
                print(f'[DEBUG] Found OneDrive in path at index {i}: {part}')
                # OneDrive migration detected, look for user profile
                # Typically: C:\Users\<USER>\OneDrive\Desktop\repos\clipmore
                # We want: C:\Users\<USER>\OneDrive
                if i > 0 and 'Users' in cwd_parts[i-2:i]:
                    # Find the Users directory and go two levels down
                    users_index = None
                    for j in range(i):
                        if 'Users' in cwd_parts[j]:
                            users_index = j
                            break
                    
                    if users_index is not None and users_index + 1 < len(cwd_parts):
                        # Build path: C:\Users\<USER>\OneDrive
                        onedrive_base = Path(*cwd_parts[:i+1])
                        print(f'[DEBUG] OneDrive base detected: {onedrive_base}')
                        return onedrive_base
            
            elif 'Users' in part and i + 1 < len(cwd_parts):
                # Standard Windows user profile
                # C:\Users\<USER>