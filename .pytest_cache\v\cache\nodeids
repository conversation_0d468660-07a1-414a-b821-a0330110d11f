["source/DB/test/test_op_clipsmore_tbl.py::TestClipsMoreTableOperations::test_assign_clip_to_business_case", "source/DB/test/test_op_clipsmore_tbl.py::TestClipsMoreTableOperations::test_assign_clip_to_business_case_and_component", "source/DB/test/test_op_clipsmore_tbl.py::TestClipsMoreTableOperations::test_assign_clip_to_different_business_cases", "source/DB/test/test_op_clipsmore_tbl.py::TestClipsMoreTableOperations::test_delete_assignment", "source/DB/test/test_op_clipsmore_tbl.py::TestClipsMoreTableOperations::test_unique_constraint", "source/DB/test/test_transactions.py::TestClipsMoreTransactions::test_full_transaction_flow", "source/test/test_export_backup.py::TestBackupSystem::test_backup_history_retrieval", "source/test/test_export_backup.py::TestBackupSystem::test_backup_manager_initialization", "source/test/test_export_backup.py::TestBackupSystem::test_backup_path_validation", "source/test/test_export_backup.py::TestBackupSystem::test_file_checksum_calculation", "source/test/test_export_backup.py::TestExportSystem::test_csv_export_with_sample_data", "source/test/test_export_backup.py::TestExportSystem::test_csv_handler_initialization", "source/test/test_export_backup.py::TestExportSystem::test_export_manager_initialization", "source/test/test_export_backup.py::TestExportSystem::test_export_path_validation", "source/test/test_export_backup.py::TestExportSystem::test_json_export_with_sample_data", "source/test/test_export_backup.py::TestExportSystem::test_json_handler_initialization"]