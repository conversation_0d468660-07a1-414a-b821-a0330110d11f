"""
UISetupManager - Handles UI initialization and setup coordination

This manager centralizes the initialization logic for the UI components,
manager coordination, and application setup flow.
"""

import tkinter as tk
from typing import Dict, Any, Optional


class UISetupManager:
    """
    Manages UI initialization and setup coordination.
    
    This class handles the complex initialization sequence of managers
    and UI components, ensuring proper dependency order and error handling.
    """
    
    def __init__(self, root: tk.Tk):
        """
        Initialize UISetupManager.
        
        Args:
            root: Main tkinter root window
        """
        print('[DEBUG] UISetupManager.__init__ called')
        self.root = root
        self.managers = {}
        self.ui_components = {}
        
    def initialize_core_managers(self, theme_manager, database_manager, validation_manager):
        """
        Initialize core managers that other managers depend on.
        
        Args:
            theme_manager: ThemeManager instance
            database_manager: DatabaseManager instance  
            validation_manager: ValidationManager instance
        """
        print('[DEBUG] UISetupManager.initialize_core_managers called')
        
        try:
            self.managers['theme_manager'] = theme_manager
            self.managers['database_manager'] = database_manager
            self.managers['validation_manager'] = validation_manager
            
            print('[DEBUG] Core managers initialized successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize core managers: {e}')
            raise
            
    def initialize_layout_manager(self, ui_layout_manager):
        """
        Initialize UI layout manager and create main layout.
        
        Args:
            ui_layout_manager: UILayoutManager instance
        """
        print('[DEBUG] UISetupManager.initialize_layout_manager called')
        
        try:
            self.managers['ui_layout_manager'] = ui_layout_manager
            
            # Create main layout
            layout_components = ui_layout_manager.create_main_layout()
            self.ui_components.update(layout_components)
            
            print('[DEBUG] Layout manager initialized successfully')
            return layout_components
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize layout manager: {e}')
            raise
            
    def create_control_buttons(self, ui_layout_manager, toggle_theme_callback, undo_callback):
        """
        Create theme toggle and undo buttons.
        
        Args:
            ui_layout_manager: UILayoutManager instance
            toggle_theme_callback: Callback for theme toggle
            undo_callback: Callback for undo action
        """
        print('[DEBUG] UISetupManager.create_control_buttons called')
        
        try:
            # Create theme toggle button
            theme_button = ui_layout_manager.create_theme_toggle(toggle_theme_callback)
            self.ui_components['theme_button'] = theme_button
            
            # Create undo button
            undo_button = ui_layout_manager.create_undo_button(undo_callback)
            self.ui_components['undo_button'] = undo_button
            
            print('[DEBUG] Control buttons created successfully')
            return theme_button, undo_button
            
        except Exception as e:
            print(f'[ERROR] Failed to create control buttons: {e}')
            raise
            
    def create_tab_control(self, ui_layout_manager):
        """
        Create tab control and tabs.
        
        Args:
            ui_layout_manager: UILayoutManager instance
        """
        print('[DEBUG] UISetupManager.create_tab_control called')
        
        try:
            tab_control, tabs = ui_layout_manager.create_tab_control()
            self.ui_components['tab_control'] = tab_control
            self.ui_components.update(tabs)
            
            print('[DEBUG] Tab control created successfully')
            return tab_control, tabs
            
        except Exception as e:
            print(f'[ERROR] Failed to create tab control: {e}')
            raise
            
    def setup_responsive_layout(self, ui_layout_manager):
        """
        Setup responsive layout.
        
        Args:
            ui_layout_manager: UILayoutManager instance
        """
        print('[DEBUG] UISetupManager.setup_responsive_layout called')
        
        try:
            ui_layout_manager.setup_responsive_layout()
            print('[DEBUG] Responsive layout setup successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to setup responsive layout: {e}')
            raise
            
    def initialize_scroll_handler(self, scroll_handler_class, tab_control):
        """
        Initialize scroll handler for global mouse wheel support.
        
        Args:
            scroll_handler_class: ScrollHandler class
            tab_control: Tab control widget
        """
        print('[DEBUG] UISetupManager.initialize_scroll_handler called')
        
        try:
            scroll_handler = scroll_handler_class(self.root, tab_control)
            self.managers['scroll_handler'] = scroll_handler
            
            print('[DEBUG] Scroll handler initialized successfully')
            return scroll_handler
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize scroll handler: {e}')
            raise
            
    def initialize_undo_manager(self, undo_manager_class, max_undo_levels=50):
        """
        Initialize undo manager.
        
        Args:
            undo_manager_class: UndoManager class
            max_undo_levels: Maximum number of undo levels
        """
        print('[DEBUG] UISetupManager.initialize_undo_manager called')
        
        try:
            undo_manager = undo_manager_class(max_undo_levels=max_undo_levels)
            self.managers['undo_manager'] = undo_manager
            
            print('[DEBUG] Undo manager initialized successfully')
            return undo_manager
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize undo manager: {e}')
            raise
            
    def get_manager(self, manager_name: str) -> Optional[Any]:
        """
        Get a manager by name.
        
        Args:
            manager_name: Name of the manager
            
        Returns:
            Manager instance or None if not found
        """
        return self.managers.get(manager_name)
        
    def get_ui_component(self, component_name: str) -> Optional[Any]:
        """
        Get a UI component by name.
        
        Args:
            component_name: Name of the UI component
            
        Returns:
            UI component or None if not found
        """
        return self.ui_components.get(component_name)
        
    def get_all_managers(self) -> Dict[str, Any]:
        """
        Get all initialized managers.
        
        Returns:
            Dictionary of all managers
        """
        return self.managers.copy()
        
    def get_all_ui_components(self) -> Dict[str, Any]:
        """
        Get all UI components.
        
        Returns:
            Dictionary of all UI components
        """
        return self.ui_components.copy()
