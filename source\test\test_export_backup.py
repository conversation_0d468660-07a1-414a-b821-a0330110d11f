#!/usr/bin/env python3
"""
Test Export and Backup Functionality
Tests the export and backup system components.
"""

import os
import sys
import unittest
import tempfile
import json
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from export.export_manager import ExportManager, ExportError
from export.format_handlers.json_handler import J<PERSON><PERSON>Hand<PERSON>
from export.format_handlers.csv_handler import CSVHandler
from backup.backup_manager import BackupManager, BackupError
from DB.db_connection import ConnectionPoolManager


class TestExportSystem(unittest.TestCase):
    """Test cases for the export system."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestExportSystem.setUp called')
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize export manager
        self.export_manager = ExportManager()
        
        # Register format handlers
        self.export_manager.register_format_handler('j<PERSON>', <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>())
        self.export_manager.register_format_handler('csv', <PERSON><PERSON><PERSON><PERSON><PERSON>())
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestExportSystem.tearDown called')
        
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_export_manager_initialization(self):
        """Test export manager initialization."""
        print('[DEBUG] test_export_manager_initialization called')
        
        self.assertIsNotNone(self.export_manager)
        self.assertIn('json', self.export_manager.get_available_formats())
        self.assertIn('csv', self.export_manager.get_available_formats())
    
    def test_json_handler_initialization(self):
        """Test JSON handler initialization."""
        print('[DEBUG] test_json_handler_initialization called')
        
        handler = JSONHandler()
        self.assertEqual(handler.format_name, "JSON")
        self.assertEqual(handler.file_extension, ".json")
        
        format_info = handler.get_format_info()
        self.assertIn('name', format_info)
        self.assertIn('features', format_info)
    
    def test_csv_handler_initialization(self):
        """Test CSV handler initialization."""
        print('[DEBUG] test_csv_handler_initialization called')
        
        handler = CSVHandler()
        self.assertEqual(handler.format_name, "CSV")
        self.assertEqual(handler.file_extension, ".csv")
        
        # Test available options
        columns = handler.get_available_columns()
        self.assertIn('content', columns)
        self.assertIn('alias', columns)
        
        encodings = handler.get_encoding_options()
        self.assertIn('utf-8', encodings)
    
    def test_export_path_validation(self):
        """Test export path validation."""
        print('[DEBUG] test_export_path_validation called')
        
        # Test valid path
        valid_path = os.path.join(self.temp_dir, 'test_export.json')
        self.assertTrue(self.export_manager.validate_export_path(valid_path))
        
        # Test invalid path (read-only directory on Windows might not work, so skip this test)
        # invalid_path = "/root/test_export.json"  # This would fail on most systems
        # self.assertFalse(self.export_manager.validate_export_path(invalid_path))
    
    def test_json_export_with_sample_data(self):
        """Test JSON export with sample data."""
        print('[DEBUG] test_json_export_with_sample_data called')
        
        # Create comprehensive cyber security sample data
        sample_data = [
            {
                'transaction_id': 26,
                'clip_id': 1,
                'alias': 'cybercriminals_intro',
                'content': 'Cybercriminals are getting smarter. Are you?\nA 76-year-old retired lawyer lost his entire life savings to a sophisticated phishing attack.',
                'timestamp': '2025-06-18T17:51:08',
                'bus_case': 'Cyber Security',
                'bus_component': None,
                'more_bus_id': 1,
                'more_comp_id': None
            },
            {
                'transaction_id': 27,
                'clip_id': 2,
                'alias': 'imposter_scams',
                'content': 'Imposter scams: They can pose as government agents by phone or email, claiming your accounts are compromised and demanding immediate payment.',
                'timestamp': '2025-06-18T17:59:12',
                'bus_case': 'Cyber Security',
                'bus_component': 'Imposter scams',
                'more_bus_id': 1,
                'more_comp_id': 1
            },
            {
                'transaction_id': 28,
                'clip_id': 3,
                'alias': 'romance_scams',
                'content': 'Romance scams: These cybercriminals play the long game— building trust over weeks by phone, text, or social media before asking for money.',
                'timestamp': '2025-06-18T17:59:32',
                'bus_case': 'Cyber Security',
                'bus_component': 'Romance scams',
                'more_bus_id': 1,
                'more_comp_id': 2
            },
            {
                'transaction_id': 29,
                'clip_id': 4,
                'alias': 'tech_support_scams',
                'content': 'Call-center scams: If you get a call or receive a computer pop up claiming your computer is experiencing problems, hang up immediately.',
                'timestamp': '2025-06-18T18:01:15',
                'bus_case': 'Cyber Security',
                'bus_component': 'Call-center scams',
                'more_bus_id': 1,
                'more_comp_id': 3
            },
            {
                'transaction_id': 30,
                'clip_id': 5,
                'alias': 'endpoint_security',
                'content': 'Endpoint security solutions protect individual devices like computers and mobile phones from malware, unauthorized access, and data breaches.',
                'timestamp': '2025-06-18T18:05:22',
                'bus_case': 'Network Security',
                'bus_component': 'Endpoint security',
                'more_bus_id': 2,
                'more_comp_id': 4
            },
            {
                'transaction_id': 31,
                'clip_id': 6,
                'alias': 'siem_systems',
                'content': 'Security Information and Event Management (SIEM) systems collect and analyze security data from across your network to detect threats.',
                'timestamp': '2025-06-18T18:08:45',
                'bus_case': 'Network Security',
                'bus_component': 'SIEM systems',
                'more_bus_id': 2,
                'more_comp_id': 5
            }
        ]
        
        # Test JSON export
        handler = JSONHandler()
        output_path = os.path.join(self.temp_dir, 'test_export.json')
        
        success = handler.export(sample_data, output_path, {'include_metadata': True})
        self.assertTrue(success)
        self.assertTrue(os.path.exists(output_path))
        
        # Verify exported content
        with open(output_path, 'r', encoding='utf-8') as f:
            exported_data = json.load(f)
        
        self.assertIn('metadata', exported_data)
        self.assertIn('data', exported_data)
        self.assertEqual(len(exported_data['data']), 2)
    
    def test_csv_export_with_sample_data(self):
        """Test CSV export with sample data."""
        print('[DEBUG] test_csv_export_with_sample_data called')
        
        # Create sample data
        sample_data = [
            {
                'transaction_id': 1,
                'clip_id': 1,
                'alias': 'test_alias',
                'content': 'Test content 1',
                'timestamp': '2024-01-01T10:00:00',
                'bus_case': 'Test Business Case',
                'bus_component': 'Test Component',
                'more_bus_id': 1,
                'more_comp_id': 1
            }
        ]
        
        # Test CSV export
        handler = CSVHandler()
        output_path = os.path.join(self.temp_dir, 'test_export.csv')
        
        success = handler.export(sample_data, output_path, {'include_header': True})
        self.assertTrue(success)
        self.assertTrue(os.path.exists(output_path))
        
        # Verify exported content
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('test_alias', content)
        self.assertIn('Test content 1', content)


class TestBackupSystem(unittest.TestCase):
    """Test cases for the backup system."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestBackupSystem.setUp called')
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize backup manager
        self.backup_manager = BackupManager()
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestBackupSystem.tearDown called')
        
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_backup_manager_initialization(self):
        """Test backup manager initialization."""
        print('[DEBUG] test_backup_manager_initialization called')
        
        self.assertIsNotNone(self.backup_manager)
        self.assertIsNotNone(self.backup_manager.connection_pool)
    
    def test_backup_path_validation(self):
        """Test backup path validation."""
        print('[DEBUG] test_backup_path_validation called')
        
        # Test valid path
        valid_path = os.path.join(self.temp_dir, 'test_backup.db')
        self.assertTrue(self.backup_manager._validate_backup_path(valid_path))
    
    def test_file_checksum_calculation(self):
        """Test file checksum calculation."""
        print('[DEBUG] test_file_checksum_calculation called')
        
        # Create a test file
        test_file = os.path.join(self.temp_dir, 'test_file.txt')
        with open(test_file, 'w') as f:
            f.write('test content')
        
        # Calculate checksum
        checksum = self.backup_manager._calculate_file_checksum(test_file)
        self.assertIsInstance(checksum, str)
        self.assertGreater(len(checksum), 0)
    
    def test_backup_history_retrieval(self):
        """Test backup history retrieval."""
        print('[DEBUG] test_backup_history_retrieval called')
        
        # This test requires the database to be set up with the migration
        try:
            history = self.backup_manager.get_backup_history(limit=10)
            self.assertIsInstance(history, list)
        except Exception as e:
            print(f'[INFO] Backup history test skipped due to database setup: {e}')


def run_tests():
    """Run all export and backup tests."""
    print("🧪 Running Export & Backup System Tests")
    print("=" * 50)

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add export tests
    suite.addTests(loader.loadTestsFromTestCase(TestExportSystem))

    # Add backup tests
    suite.addTests(loader.loadTestsFromTestCase(TestBackupSystem))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)

    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors

    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")

    if failures == 0 and errors == 0:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        return False


if __name__ == "__main__":
    run_tests()
