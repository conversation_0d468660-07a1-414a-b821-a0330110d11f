"""
TabManager - Tab control system for ClipsMore application

This module handles the main tab control interface including:
- Tab creation and management
- Tab switching and navigation
- Integration with content managers
- Theme-aware tab styling

Author: ClipsMore Development Team
Date: 2025-06-16
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Type, Callable


class TabManager:
    """
    Manages the main tab control system for the ClipsMore application.
    
    Handles tab creation, switching, and integration with content managers
    for the Clips, More, and About tabs.
    """
    
    def __init__(self, parent: tk.Widget, theme_manager):
        """
        Initialize TabManager with parent widget and theme manager.
        
        Args:
            parent: Parent widget for the tab control
            theme_manager: ThemeManager instance for styling
        """
        print('[DEBUG] TabManager.__init__ called')
        
        self.parent = parent
        self.theme_manager = theme_manager
        self.notebook = None
        self.tabs = {}
        self.tab_frames = {}
        self.current_tab_index = 0
        
        # Tab configuration
        self.tab_configs = [
            {
                'name': 'Clips',
                'title': 'Clips',
                'manager_type': 'clip_manager',
                'init_method': 'create_clips_interface'
            },
            {
                'name': 'More',
                'title': 'More',
                'manager_type': 'tree_manager',
                'init_method': 'create_tree_interface'
            },
            {
                'name': 'About',
                'title': 'About',
                'manager_type': 'documentation_manager',
                'init_method': 'create_documentation_interface'
            }
        ]
        
        print('[DEBUG] TabManager initialized successfully')
    
    def create_tab_control(self, managers: Dict[str, Any]) -> ttk.Notebook:
        """
        Create the main tab control with all tabs.
        
        Args:
            managers: Dictionary of manager instances
            
        Returns:
            TTK Notebook widget containing all tabs
        """
        print('[DEBUG] TabManager.create_tab_control called')
        
        try:
            # Create main notebook
            self.notebook = ttk.Notebook(self.parent)
            
            # Configure notebook styling
            self._configure_notebook_style()
            
            # Create all tabs
            for tab_config in self.tab_configs:
                self._create_tab(tab_config, managers)
            
            # Bind tab change events
            self.notebook.bind('<<NotebookTabChanged>>', self._on_tab_changed)
            
            print(f'[DEBUG] Created tab control with {len(self.tab_configs)} tabs')
            return self.notebook
            
        except Exception as e:
            print(f'[ERROR] Failed to create tab control: {e}')
            return ttk.Notebook(self.parent)
    
    def _create_tab(self, tab_config: Dict[str, str], managers: Dict[str, Any]):
        """
        Create a single tab with its content.
        
        Args:
            tab_config: Tab configuration dictionary
            managers: Dictionary of manager instances
        """
        try:
            tab_name = tab_config['name']
            tab_title = tab_config['title']
            manager_type = tab_config['manager_type']
            init_method = tab_config['init_method']
            
            print(f'[DEBUG] Creating tab: {tab_name}')
            
            # Create tab frame
            tab_frame = tk.Frame(self.notebook, bg=self.theme_manager.bg_color)
            
            # Get the appropriate manager
            manager = managers.get(manager_type)
            if manager and hasattr(manager, init_method):
                # Call the manager's initialization method
                init_func = getattr(manager, init_method)
                content_frame = init_func()
                
                # If the method returns a frame, pack it
                if content_frame and isinstance(content_frame, tk.Widget):
                    content_frame.pack(fill='both', expand=True)
                
            else:
                # Create placeholder content if manager not available
                self._create_placeholder_content(tab_frame, tab_name, manager_type)
            
            # Add tab to notebook
            self.notebook.add(tab_frame, text=tab_title)
            
            # Store tab references
            self.tabs[tab_name] = {
                'frame': tab_frame,
                'config': tab_config,
                'manager': manager
            }
            self.tab_frames[tab_name] = tab_frame
            
            print(f'[DEBUG] Successfully created tab: {tab_name}')
            
        except Exception as e:
            print(f'[ERROR] Failed to create tab {tab_config.get("name", "unknown")}: {e}')
    
    def _create_placeholder_content(self, tab_frame: tk.Frame, tab_name: str, manager_type: str):
        """
        Create placeholder content for tabs when manager is not available.
        
        Args:
            tab_frame: Tab frame to add content to
            tab_name: Name of the tab
            manager_type: Type of manager expected
        """
        placeholder_frame = tk.Frame(tab_frame, bg=self.theme_manager.bg_color)
        placeholder_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(placeholder_frame,
                              text=f"{tab_name} Tab",
                              font=('Segoe UI', 16, 'bold'),
                              bg=self.theme_manager.bg_color,
                              fg=self.theme_manager.fg_color)
        title_label.pack(pady=(0, 10))
        
        # Message
        message_label = tk.Label(placeholder_frame,
                                text=f"Content manager ({manager_type}) not available.\nThis tab will be populated when the manager is initialized.",
                                font=('Segoe UI', 10),
                                bg=self.theme_manager.bg_color,
                                fg=self.theme_manager.fg_color,
                                justify=tk.CENTER)
        message_label.pack(pady=10)
    
    def _configure_notebook_style(self):
        """
        Configure the notebook styling to match the theme.
        """
        try:
            style = ttk.Style()
            
            # Configure notebook style
            style.configure('TNotebook',
                          background=self.theme_manager.bg_color,
                          borderwidth=0)
            
            style.configure('TNotebook.Tab',
                          background=self.theme_manager.button_bg,
                          foreground=self.theme_manager.button_fg,
                          padding=[12, 8],
                          font=('Segoe UI', 10))
            
            style.map('TNotebook.Tab',
                     background=[('selected', self.theme_manager.tree_select),
                               ('active', self.theme_manager.tree_hover)],
                     foreground=[('selected', self.theme_manager.fg_color),
                               ('active', self.theme_manager.fg_color)])
            
            print('[DEBUG] Notebook styling configured')
            
        except Exception as e:
            print(f'[ERROR] Failed to configure notebook style: {e}')
    
    def add_tab(self, name: str, title: str, content_widget: tk.Widget) -> tk.Frame:
        """
        Add a new tab to the notebook.
        
        Args:
            name: Internal name for the tab
            title: Display title for the tab
            content_widget: Widget to display in the tab
            
        Returns:
            Tab frame
        """
        print(f'[DEBUG] TabManager.add_tab called for {name}')
        
        try:
            # Create tab frame
            tab_frame = tk.Frame(self.notebook, bg=self.theme_manager.bg_color)
            
            # Add content widget
            if content_widget:
                content_widget.pack(in_=tab_frame, fill='both', expand=True)
            
            # Add tab to notebook
            self.notebook.add(tab_frame, text=title)
            
            # Store tab reference
            self.tabs[name] = {
                'frame': tab_frame,
                'title': title,
                'content': content_widget
            }
            self.tab_frames[name] = tab_frame
            
            print(f'[DEBUG] Added tab: {name}')
            return tab_frame
            
        except Exception as e:
            print(f'[ERROR] Failed to add tab {name}: {e}')
            return tk.Frame(self.notebook)
    
    def get_current_tab_index(self) -> int:
        """
        Get the index of the currently selected tab.
        
        Returns:
            Current tab index
        """
        try:
            if self.notebook:
                return self.notebook.index(self.notebook.select())
            return 0
            
        except Exception as e:
            print(f'[ERROR] Failed to get current tab index: {e}')
            return 0
    
    def get_current_tab_name(self) -> Optional[str]:
        """
        Get the name of the currently selected tab.
        
        Returns:
            Current tab name or None
        """
        try:
            current_index = self.get_current_tab_index()
            tab_names = list(self.tabs.keys())
            
            if 0 <= current_index < len(tab_names):
                return tab_names[current_index]
            return None
            
        except Exception as e:
            print(f'[ERROR] Failed to get current tab name: {e}')
            return None
    
    def switch_to_tab(self, identifier) -> bool:
        """
        Switch to a specific tab by index or name.
        
        Args:
            identifier: Tab index (int) or tab name (str)
            
        Returns:
            True if switch was successful, False otherwise
        """
        print(f'[DEBUG] TabManager.switch_to_tab called with identifier={identifier}')
        
        try:
            if isinstance(identifier, int):
                # Switch by index
                if 0 <= identifier < self.notebook.index('end'):
                    self.notebook.select(identifier)
                    self.current_tab_index = identifier
                    return True
            elif isinstance(identifier, str):
                # Switch by name
                if identifier in self.tabs:
                    tab_frame = self.tabs[identifier]['frame']
                    self.notebook.select(tab_frame)
                    self.current_tab_index = self.get_current_tab_index()
                    return True
            
            return False
            
        except Exception as e:
            print(f'[ERROR] Failed to switch to tab {identifier}: {e}')
            return False
    
    def _on_tab_changed(self, event):
        """
        Handle tab change events.
        
        Args:
            event: Tkinter event object
        """
        try:
            self.current_tab_index = self.get_current_tab_index()
            current_tab_name = self.get_current_tab_name()
            
            print(f'[DEBUG] Tab changed to: {current_tab_name} (index: {self.current_tab_index})')
            
            # Notify managers of tab change if needed
            self._notify_tab_change(current_tab_name)
            
        except Exception as e:
            print(f'[ERROR] Failed to handle tab change: {e}')
    
    def _notify_tab_change(self, tab_name: Optional[str]):
        """
        Notify relevant managers of tab changes.
        
        Args:
            tab_name: Name of the newly selected tab
        """
        try:
            if tab_name and tab_name in self.tabs:
                tab_info = self.tabs[tab_name]
                manager = tab_info.get('manager')
                
                # Call manager's tab focus method if it exists
                if manager and hasattr(manager, 'on_tab_focus'):
                    manager.on_tab_focus()
                
        except Exception as e:
            print(f'[ERROR] Failed to notify tab change: {e}')
    
    def get_tab_frame(self, tab_name: str) -> Optional[tk.Frame]:
        """
        Get the frame for a specific tab.
        
        Args:
            tab_name: Name of the tab
            
        Returns:
            Tab frame or None if not found
        """
        return self.tab_frames.get(tab_name)
    
    def get_tab_manager(self, tab_name: str) -> Optional[Any]:
        """
        Get the manager associated with a specific tab.
        
        Args:
            tab_name: Name of the tab
            
        Returns:
            Manager instance or None if not found
        """
        tab_info = self.tabs.get(tab_name)
        return tab_info.get('manager') if tab_info else None
    
    def refresh_tab(self, tab_name: str):
        """
        Refresh the content of a specific tab.
        
        Args:
            tab_name: Name of the tab to refresh
        """
        print(f'[DEBUG] TabManager.refresh_tab called for {tab_name}')
        
        try:
            if tab_name in self.tabs:
                tab_info = self.tabs[tab_name]
                manager = tab_info.get('manager')
                
                # Call manager's refresh method if it exists
                if manager and hasattr(manager, 'refresh'):
                    manager.refresh()
                
                print(f'[DEBUG] Refreshed tab: {tab_name}')
            else:
                print(f'[WARNING] Tab not found: {tab_name}')
                
        except Exception as e:
            print(f'[ERROR] Failed to refresh tab {tab_name}: {e}')
    
    def refresh_all_tabs(self):
        """
        Refresh the content of all tabs.
        """
        print('[DEBUG] TabManager.refresh_all_tabs called')
        
        for tab_name in self.tabs.keys():
            self.refresh_tab(tab_name)
    
    def update_theme(self):
        """
        Update tab styling when theme changes.
        """
        print('[DEBUG] TabManager.update_theme called')
        
        try:
            # Reconfigure notebook style
            self._configure_notebook_style()
            
            # Update tab frame backgrounds
            for tab_info in self.tabs.values():
                tab_frame = tab_info['frame']
                tab_frame.config(bg=self.theme_manager.bg_color)
            
            print('[DEBUG] Tab theme updated')
            
        except Exception as e:
            print(f'[ERROR] Failed to update tab theme: {e}')
