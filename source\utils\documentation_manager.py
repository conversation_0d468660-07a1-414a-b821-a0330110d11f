"""
DocumentationManager - Documentation display system for ClipsMore application

This module handles the About tab functionality including:
- Multi-document viewer with tabbed interface
- Markdown rendering with syntax highlighting
- Dynamic document loading from various sources
- Responsive layout and theme integration

Author: ClipsMore Development Team
Date: 2025-06-16
"""

import tkinter as tk
from tkinter import ttk
import os
import re
from typing import Dict, List, Tuple, Optional


class DocumentationManager:
    """
    Manages the documentation display system for the ClipsMore application.
    
    Provides a multi-document viewer with markdown rendering capabilities,
    tabbed interface, and theme integration for the About tab.
    """
    
    def __init__(self, parent: tk.Widget, theme_manager):
        """
        Initialize DocumentationManager with parent widget and theme manager.
        
        Args:
            parent: Parent widget (About tab frame)
            theme_manager: ThemeManager instance for styling
        """
        print('[DEBUG] DocumentationManager.__init__ called')
        
        self.parent = parent
        self.theme_manager = theme_manager
        self.docs_notebook = None
        self.doc_tabs = {}
        
        # Define documentation files to load (relative to source directory)
        self.doc_files = [
            ("README", "../README.md"),
            ("User Guide", "../docs/user/User_Guide.md"),
            ("Technical Overview", "../docs/technical/README.md"),
            ("System Architecture", "../docs/technical/architecture/System_Architecture.md"),
            ("Database Schema", "../docs/technical/database/ER_Diagram.md"),
            ("UML Diagrams", "../docs/technical/uml/Class_Diagrams.md"),
            ("C4 Model", "../docs/technical/c4/C4_Model.md"),
            ("Dependencies", "../docs/technical/dependencies/Dependency_Analysis.md")
        ]
        
        print('[DEBUG] DocumentationManager initialized successfully')
    
    def create_documentation_interface(self) -> tk.Frame:
        """
        Create the main documentation interface with tabbed document viewer.
        
        Returns:
            Main documentation frame
        """
        print('[DEBUG] DocumentationManager.create_documentation_interface called')
        
        try:
            # Create main documentation frame
            main_frame = tk.Frame(self.parent, bg=self.theme_manager.bg_color)
            main_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Create sub-notebook for documentation
            self.docs_notebook = ttk.Notebook(main_frame)
            self.docs_notebook.pack(fill='both', expand=True)
            
            # Load documentation tabs
            self.load_documentation_tabs()
            
            print('[DEBUG] Documentation interface created successfully')
            return main_frame
            
        except Exception as e:
            print(f'[ERROR] Failed to create documentation interface: {e}')
            return tk.Frame(self.parent)
    
    def load_documentation_tabs(self):
        """
        Load all documentation tabs from the defined file list.
        """
        print('[DEBUG] DocumentationManager.load_documentation_tabs called')
        
        try:
            # Create tabs for each document
            for tab_name, file_path in self.doc_files:
                self.create_doc_tab(tab_name, file_path)
            
            print(f'[DEBUG] Loaded {len(self.doc_files)} documentation tabs')
            
        except Exception as e:
            print(f'[ERROR] Failed to load documentation tabs: {e}')
    
    def create_doc_tab(self, title: str, file_path: str):
        """
        Create a documentation tab with rendered markdown content.
        
        Args:
            title: Tab title
            file_path: Path to the markdown file
        """
        print(f'[DEBUG] DocumentationManager.create_doc_tab called for {title}: {file_path}')
        
        try:
            # Create frame for this document
            doc_frame = tk.Frame(self.docs_notebook, bg=self.theme_manager.bg_color)
            self.docs_notebook.add(doc_frame, text=title)
            
            # Create text widget with scrollbar
            text_frame = tk.Frame(doc_frame, bg=self.theme_manager.bg_color)
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Create scrollbar
            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Create text widget with better formatting
            text_widget = tk.Text(text_frame,
                                bg=self.theme_manager.bg_color,
                                fg=self.theme_manager.fg_color,
                                wrap=tk.WORD,
                                font=('Segoe UI', 10),
                                yscrollcommand=scrollbar.set,
                                padx=15,
                                pady=15)
            text_widget.pack(side=tk.LEFT, fill='both', expand=True)
            scrollbar.config(command=text_widget.yview)
            
            # Load and display content
            content = self._load_document_content(file_path)
            
            # Configure text tags for markdown-like formatting
            self._configure_markdown_tags(text_widget)
            
            # Render markdown content
            self.render_markdown(text_widget, content)
            
            text_widget.config(state='disabled')
            
            # Store tab reference
            self.doc_tabs[title] = {
                'frame': doc_frame,
                'text_widget': text_widget,
                'file_path': file_path
            }
            
            print(f'[DEBUG] Created documentation tab: {title}')
            
        except Exception as e:
            print(f'[ERROR] Failed to create doc tab {title}: {e}')
    
    def _load_document_content(self, file_path: str) -> str:
        """
        Load document content from file with fallback handling.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Document content as string
        """
        try:
            abs_path = os.path.abspath(file_path)
            print(f'[DEBUG] Looking for documentation file: {abs_path}')
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f'[DEBUG] Successfully loaded {len(content)} characters from {file_path}')
                return content
            else:
                # Try alternative paths
                alternative_paths = [
                    file_path,
                    os.path.join('..', file_path),
                    os.path.join('..', '..', file_path),
                    file_path.replace('../', '')
                ]
                
                for alt_path in alternative_paths:
                    if os.path.exists(alt_path):
                        with open(alt_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        print(f'[DEBUG] Found documentation at alternative path: {alt_path}')
                        return content
                
                # Generate "not found" content
                return self._generate_not_found_content(file_path, abs_path, alternative_paths)
                
        except Exception as e:
            return f"""# Error Loading Document
            
**Error**: {str(e)}
**File Path**: `{file_path}`

## Details:
An error occurred while trying to load this documentation file.

---
*Please check the file path and permissions.*"""
    
    def _generate_not_found_content(self, file_path: str, abs_path: str, alternative_paths: List[str]) -> str:
        """
        Generate content for missing documentation files.
        
        Args:
            file_path: Original file path
            abs_path: Absolute file path
            alternative_paths: List of alternative paths checked
            
        Returns:
            Generated content string
        """
        try:
            available_files = [f for f in os.listdir('.') if f.endswith('.md')]
        except:
            available_files = []
        
        return f"""# Document Not Found
        
**File Path**: `{file_path}`
**Absolute Path**: `{abs_path}`

## Possible Reasons:
- The document may not have been created yet
- The file may be in a different location
- The file path may need to be updated

## Expected Location:
This document should be located at: `{file_path}`

## Alternative Paths Checked:
{chr(10).join(f'- {path}' for path in alternative_paths)}

## Available Files in Current Directory:
{chr(10).join(f'- {f}' for f in available_files)}

---
*This is an auto-generated message from the ClipsMore documentation system.*"""
    
    def _configure_markdown_tags(self, text_widget: tk.Text):
        """
        Configure text tags for markdown-like formatting using ThemeManager.
        
        Args:
            text_widget: Text widget to configure
        """
        try:
            # Get markdown colors from theme manager
            colors = self.theme_manager.get_markdown_colors()
            
            # Header styles
            text_widget.tag_configure('h1', font=('Segoe UI', 16, 'bold'), foreground=colors['h1'])
            text_widget.tag_configure('h2', font=('Segoe UI', 14, 'bold'), foreground=colors['h2'])
            text_widget.tag_configure('h3', font=('Segoe UI', 12, 'bold'), foreground=colors['h3'])
            text_widget.tag_configure('h4', font=('Segoe UI', 11, 'bold'), foreground=colors['h4'])
            
            # Text styles
            text_widget.tag_configure('bold', font=('Segoe UI', 10, 'bold'))
            text_widget.tag_configure('italic', font=('Segoe UI', 10, 'italic'))
            text_widget.tag_configure('code', font=('Consolas', 9),
                                    background=colors['code_bg'], foreground=colors['code_fg'])
            text_widget.tag_configure('code_block', font=('Consolas', 9),
                                    background=colors['code_block_bg'], foreground=colors['code_block_fg'],
                                    lmargin1=20, lmargin2=20)
            
            # List styles
            text_widget.tag_configure('bullet', lmargin1=20, lmargin2=40)
            text_widget.tag_configure('numbered', lmargin1=20, lmargin2=40)
            
            # Link style
            text_widget.tag_configure('link', foreground=colors['link'], underline=True)
            
            # Quote style
            text_widget.tag_configure('quote', lmargin1=20, lmargin2=20,
                                    background=colors['quote_bg'], foreground=colors['quote_fg'])
            
        except Exception as e:
            print(f'[ERROR] Failed to configure markdown tags: {e}')
    
    def render_markdown(self, text_widget: tk.Text, content: str) -> str:
        """
        Render markdown content with basic formatting.
        
        Args:
            text_widget: Text widget to render content in
            content: Markdown content to render
            
        Returns:
            Rendered content (for potential future use)
        """
        try:
            text_widget.config(state='normal')
            text_widget.delete('1.0', tk.END)
            
            lines = content.split('\n')
            in_code_block = False
            
            for line in lines:
                # Handle code blocks (including mermaid as plain code)
                if line.strip().startswith('```'):
                    in_code_block = not in_code_block
                    text_widget.insert(tk.INSERT, '\n')
                    continue
                
                if in_code_block:
                    text_widget.insert(tk.INSERT, line + '\n', 'code_block')
                    continue
                
                # Handle headers
                if line.startswith('# '):
                    text_widget.insert(tk.INSERT, line[2:] + '\n\n', 'h1')
                elif line.startswith('## '):
                    text_widget.insert(tk.INSERT, line[3:] + '\n\n', 'h2')
                elif line.startswith('### '):
                    text_widget.insert(tk.INSERT, line[4:] + '\n\n', 'h3')
                elif line.startswith('#### '):
                    text_widget.insert(tk.INSERT, line[5:] + '\n\n', 'h4')
                
                # Handle lists
                elif line.strip().startswith('- ') or line.strip().startswith('* '):
                    text_widget.insert(tk.INSERT, '• ' + line.strip()[2:] + '\n', 'bullet')
                elif line.strip().startswith('1. ') or any(line.strip().startswith(f'{i}. ') for i in range(1, 10)):
                    # Find the number and dot
                    parts = line.strip().split('. ', 1)
                    if len(parts) == 2:
                        text_widget.insert(tk.INSERT, f'{parts[0]}. {parts[1]}\n', 'numbered')
                    else:
                        text_widget.insert(tk.INSERT, line + '\n')
                
                # Handle quotes
                elif line.strip().startswith('> '):
                    text_widget.insert(tk.INSERT, line[2:] + '\n', 'quote')
                
                # Handle regular text with inline formatting
                else:
                    self._render_inline_formatting(text_widget, line)
                    text_widget.insert(tk.INSERT, '\n')
            
            return content
            
        except Exception as e:
            print(f'[ERROR] Failed to render markdown content: {e}')
            # Fallback to plain text
            text_widget.config(state='normal')
            text_widget.delete('1.0', tk.END)
            text_widget.insert('1.0', content)
            return content
    
    def _render_inline_formatting(self, text_widget: tk.Text, line: str):
        """
        Render inline markdown formatting like bold, italic, code.
        
        Args:
            text_widget: Text widget to render in
            line: Line of text to process
        """
        try:
            # Process the line for inline formatting
            remaining = line
            
            while remaining:
                # Find the next formatting marker
                bold_match = re.search(r'\*\*(.*?)\*\*', remaining)
                italic_match = re.search(r'\*(.*?)\*', remaining)
                code_match = re.search(r'`(.*?)`', remaining)
                link_match = re.search(r'\[([^\]]+)\]\([^)]+\)', remaining)
                
                # Find the earliest match
                matches = []
                if bold_match:
                    matches.append((bold_match.start(), bold_match.end(), 'bold', bold_match.group(1)))
                if italic_match:
                    matches.append((italic_match.start(), italic_match.end(), 'italic', italic_match.group(1)))
                if code_match:
                    matches.append((code_match.start(), code_match.end(), 'code', code_match.group(1)))
                if link_match:
                    matches.append((link_match.start(), link_match.end(), 'link', link_match.group(1)))
                
                if not matches:
                    # No more formatting, insert the rest as plain text
                    text_widget.insert(tk.INSERT, remaining)
                    break
                
                # Sort by position and take the first one
                matches.sort(key=lambda x: x[0])
                start, end, tag, text = matches[0]
                
                # Insert text before the match
                if start > 0:
                    text_widget.insert(tk.INSERT, remaining[:start])
                
                # Insert the formatted text
                text_widget.insert(tk.INSERT, text, tag)
                
                # Continue with the rest
                remaining = remaining[end:]
                
        except Exception as e:
            print(f'[ERROR] Failed to render inline formatting: {e}')
            # Fallback to plain text
            text_widget.insert(tk.INSERT, line)
    
    def refresh_documentation(self):
        """
        Refresh all documentation tabs by reloading content.
        """
        print('[DEBUG] DocumentationManager.refresh_documentation called')
        
        try:
            for title, tab_info in self.doc_tabs.items():
                content = self._load_document_content(tab_info['file_path'])
                text_widget = tab_info['text_widget']
                
                text_widget.config(state='normal')
                self.render_markdown(text_widget, content)
                text_widget.config(state='disabled')
            
            print('[DEBUG] Documentation refreshed successfully')
            
        except Exception as e:
            print(f'[ERROR] Failed to refresh documentation: {e}')
    
    def get_current_tab(self) -> Optional[str]:
        """
        Get the currently selected documentation tab.
        
        Returns:
            Name of current tab or None if no tab selected
        """
        try:
            if self.docs_notebook:
                current_index = self.docs_notebook.index(self.docs_notebook.select())
                tab_names = list(self.doc_tabs.keys())
                if 0 <= current_index < len(tab_names):
                    return tab_names[current_index]
            return None
            
        except Exception as e:
            print(f'[ERROR] Failed to get current tab: {e}')
            return None
