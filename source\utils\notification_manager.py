#!/usr/bin/env python3
"""
NotificationManager for ClipsMore v2.0
Provides centralized toast-style notifications to replace scattered messagebox calls.
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Dict, Any, Callable
import threading
import time

class NotificationManager:
    """
    Centralized notification system providing toast-style notifications.
    
    Features:
    - Non-blocking toast notifications
    - Color-coded status indicators (success/warning/error/info)
    - Configurable display duration
    - Theme-aware styling
    - Progress indicators for long operations
    - Queue management for multiple notifications
    """
    
    def __init__(self, root: tk.Tk, theme_manager=None):
        """
        Initialize NotificationManager.
        
        Args:
            root: Main tkinter root window
            theme_manager: Optional theme manager for styling
        """
        print('[DEBUG] NotificationManager.__init__ called')
        self.root = root
        self.theme_manager = theme_manager
        
        # Notification queue and display management
        self.notification_queue = []
        self.current_notification = None
        self.notification_window = None
        
        # Default settings
        self.default_duration = 3000  # 3 seconds
        self.fade_duration = 500      # 0.5 seconds
        self.max_width = 400
        self.max_notifications = 5
        
        # Position settings
        self.notification_spacing = 10
        self.edge_padding = 20
        
        # Initialize notification styles
        self._initialize_styles()
        
    def _initialize_styles(self):
        """Initialize notification styles based on theme."""
        print('[DEBUG] NotificationManager._initialize_styles called')
        
        # Get theme colors if theme manager is available
        if self.theme_manager:
            colors = self.theme_manager.get_theme_colors()
            bg_base = colors['bg_color']
            fg_base = colors['fg_color']
            is_dark = colors['dark_mode']
        else:
            # Default light theme colors
            bg_base = '#ffffff'
            fg_base = '#000000'
            is_dark = False
        
        # Define notification type styles
        self.styles = {
            'success': {
                'bg': '#4CAF50' if not is_dark else '#2E7D32',
                'fg': '#ffffff',
                'icon': '✓',
                'border': '#45a049' if not is_dark else '#1B5E20'
            },
            'error': {
                'bg': '#f44336' if not is_dark else '#C62828',
                'fg': '#ffffff', 
                'icon': '✗',
                'border': '#d32f2f' if not is_dark else '#8E0000'
            },
            'warning': {
                'bg': '#ff9800' if not is_dark else '#E65100',
                'fg': '#ffffff',
                'icon': '⚠',
                'border': '#f57c00' if not is_dark else '#BF360C'
            },
            'info': {
                'bg': '#2196F3' if not is_dark else '#1565C0',
                'fg': '#ffffff',
                'icon': 'ℹ',
                'border': '#1976D2' if not is_dark else '#0D47A1'
            }
        }
        
    def show_success(self, message: str, duration: Optional[int] = None):
        """Show success notification."""
        print(f'[DEBUG] NotificationManager.show_success called: {message}')
        self._show_notification('success', message, duration)
        
    def show_error(self, message: str, duration: Optional[int] = None):
        """Show error notification."""
        print(f'[DEBUG] NotificationManager.show_error called: {message}')
        self._show_notification('error', message, duration)
        
    def show_warning(self, message: str, duration: Optional[int] = None):
        """Show warning notification."""
        print(f'[DEBUG] NotificationManager.show_warning called: {message}')
        self._show_notification('warning', message, duration)
        
    def show_info(self, message: str, duration: Optional[int] = None):
        """Show info notification."""
        print(f'[DEBUG] NotificationManager.show_info called: {message}')
        self._show_notification('info', message, duration)
        
    def _show_notification(self, notification_type: str, message: str, duration: Optional[int] = None):
        """
        Show a notification of the specified type.
        
        Args:
            notification_type: Type of notification (success, error, warning, info)
            message: Message to display
            duration: Display duration in milliseconds (None for default)
        """
        print(f'[DEBUG] NotificationManager._show_notification called: {notification_type}, {message}')
        
        if duration is None:
            duration = self.default_duration
            
        # Create notification data
        notification_data = {
            'type': notification_type,
            'message': message,
            'duration': duration,
            'timestamp': time.time()
        }
        
        # Add to queue
        self.notification_queue.append(notification_data)
        
        # Process queue if not already processing
        if self.current_notification is None:
            self._process_notification_queue()
            
    def _process_notification_queue(self):
        """Process the notification queue."""
        print('[DEBUG] NotificationManager._process_notification_queue called')
        
        if not self.notification_queue:
            self.current_notification = None
            return
            
        # Get next notification
        self.current_notification = self.notification_queue.pop(0)
        
        # Create and show notification window
        self._create_notification_window(self.current_notification)
        
    def _create_notification_window(self, notification_data: Dict[str, Any]):
        """
        Create and display a notification window.
        
        Args:
            notification_data: Dictionary containing notification information
        """
        print(f'[DEBUG] NotificationManager._create_notification_window called for {notification_data["type"]}')
        
        try:
            # Create toplevel window
            self.notification_window = tk.Toplevel(self.root)
            self.notification_window.withdraw()  # Hide initially
            
            # Configure window
            self.notification_window.overrideredirect(True)  # Remove window decorations
            self.notification_window.attributes('-topmost', True)  # Keep on top
            
            # Get style for notification type
            style = self.styles.get(notification_data['type'], self.styles['info'])
            
            # Create main frame
            main_frame = tk.Frame(
                self.notification_window,
                bg=style['bg'],
                relief='raised',
                bd=2
            )
            main_frame.pack(fill='both', expand=True, padx=2, pady=2)
            
            # Create content frame
            content_frame = tk.Frame(main_frame, bg=style['bg'])
            content_frame.pack(fill='both', expand=True, padx=10, pady=8)
            
            # Add icon
            icon_label = tk.Label(
                content_frame,
                text=style['icon'],
                bg=style['bg'],
                fg=style['fg'],
                font=('Arial', 16, 'bold')
            )
            icon_label.pack(side='left', padx=(0, 10))
            
            # Add message
            message_label = tk.Label(
                content_frame,
                text=notification_data['message'],
                bg=style['bg'],
                fg=style['fg'],
                font=('Arial', 10),
                wraplength=self.max_width - 80,
                justify='left'
            )
            message_label.pack(side='left', fill='both', expand=True)
            
            # Add close button
            close_button = tk.Label(
                content_frame,
                text='×',
                bg=style['bg'],
                fg=style['fg'],
                font=('Arial', 14, 'bold'),
                cursor='hand2'
            )
            close_button.pack(side='right', padx=(10, 0))
            close_button.bind('<Button-1>', lambda e: self._close_notification())
            
            # Position window
            self._position_notification_window()
            
            # Show window with fade-in effect
            self._show_with_fade_in()
            
            # Schedule auto-close
            self.root.after(notification_data['duration'], self._close_notification)
            
        except Exception as e:
            print(f'[ERROR] Failed to create notification window: {e}')
            self._process_notification_queue()  # Continue with next notification
            
    def _position_notification_window(self):
        """Position the notification window in the bottom-right corner."""
        print('[DEBUG] NotificationManager._position_notification_window called')
        
        try:
            # Update window to get accurate size
            self.notification_window.update_idletasks()
            
            # Get window dimensions
            window_width = self.notification_window.winfo_reqwidth()
            window_height = self.notification_window.winfo_reqheight()
            
            # Get screen dimensions
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # Calculate position (bottom-right corner)
            x = screen_width - window_width - self.edge_padding
            y = screen_height - window_height - self.edge_padding - 50  # Account for taskbar
            
            # Set position
            self.notification_window.geometry(f'+{x}+{y}')
            
        except Exception as e:
            print(f'[ERROR] Failed to position notification window: {e}')
            
    def _show_with_fade_in(self):
        """Show notification window with fade-in effect."""
        print('[DEBUG] NotificationManager._show_with_fade_in called')
        
        try:
            # Show window
            self.notification_window.deiconify()
            
            # Simple fade-in by adjusting alpha (if supported)
            try:
                self.notification_window.attributes('-alpha', 0.0)
                self._fade_in_step(0.0)
            except tk.TclError:
                # Alpha not supported, just show normally
                pass
                
        except Exception as e:
            print(f'[ERROR] Failed to show notification with fade-in: {e}')
            
    def _fade_in_step(self, alpha: float):
        """Perform one step of fade-in animation."""
        if self.notification_window and alpha < 1.0:
            alpha = min(1.0, alpha + 0.1)
            try:
                self.notification_window.attributes('-alpha', alpha)
                self.root.after(50, lambda: self._fade_in_step(alpha))
            except tk.TclError:
                pass
                
    def _close_notification(self):
        """Close the current notification and process next in queue."""
        print('[DEBUG] NotificationManager._close_notification called')
        
        try:
            if self.notification_window:
                self.notification_window.destroy()
                self.notification_window = None
                
            # Process next notification in queue
            self.root.after(100, self._process_notification_queue)
            
        except Exception as e:
            print(f'[ERROR] Failed to close notification: {e}')
            self.current_notification = None
            self._process_notification_queue()
            
    def update_theme(self):
        """Update notification styles when theme changes."""
        print('[DEBUG] NotificationManager.update_theme called')
        self._initialize_styles()
        
    def clear_all_notifications(self):
        """Clear all pending notifications."""
        print('[DEBUG] NotificationManager.clear_all_notifications called')
        self.notification_queue.clear()
        if self.notification_window:
            self._close_notification()

# NOTE: All new code should include debug print statements at the start of every function/method.
