#!/usr/bin/env python3
"""
Restore Manager for ClipsMore
Handles database restoration from backup files with verification and progress tracking.
"""

import os
import sys
import sqlite3
import gzip
import shutil
import hashlib
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DB.db_connection import ConnectionPoolManager


class RestoreError(Exception):
    """Exception raised for restore operation errors."""
    pass


class RestoreManager:
    """
    Core restore manager for ClipsMore application.
    Handles database restoration from backup files with verification and progress tracking.
    """
    
    def __init__(self, database_manager=None):
        """Initialize the restore manager."""
        print('[DEBUG] RestoreManager.__init__ called')
        self.database_manager = database_manager
        self.connection_pool = ConnectionPoolManager()
        self.progress_callback = None
        self.cancel_requested = False
        
        # Default restore configuration
        self.default_restore_config = {
            'verification': True,
            'backup_current': True,
            'restore_type': 'full',
            'overwrite_existing': False
        }
    
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """Set callback for progress updates."""
        print('[DEBUG] RestoreManager.set_progress_callback called')
        self.progress_callback = callback
    
    def cancel_restore(self):
        """Cancel the current restore operation."""
        print('[DEBUG] RestoreManager.cancel_restore called')
        self.cancel_requested = True
    
    def _update_progress(self, percentage: int, message: str):
        """Update progress if callback is set."""
        if self.progress_callback:
            self.progress_callback(percentage, message)
    
    def _check_cancellation(self):
        """Check if restore operation was cancelled."""
        if self.cancel_requested:
            raise RestoreError("Restore operation was cancelled")
    
    def restore_from_backup(self, backup_path: str, restore_config: Dict[str, Any] = None) -> bool:
        """
        Restore database from backup file.
        
        Args:
            backup_path: Path to backup file
            restore_config: Restore configuration options
                - verification: Verify backup before restore (default: True)
                - backup_current: Create backup of current database (default: True)
                - restore_type: Type of restore ('full', 'selective')
                - overwrite_existing: Overwrite existing data (default: False)
        
        Returns:
            True if restore successful, False otherwise
        """
        print(f'[DEBUG] RestoreManager.restore_from_backup called for {backup_path}')
        
        try:
            self.cancel_requested = False
            config = {**self.default_restore_config, **(restore_config or {})}
            
            self._update_progress(0, "Starting restore...")
            
            # Validate backup file
            if not self._validate_backup_file(backup_path):
                raise RestoreError(f"Invalid backup file: {backup_path}")
            
            self._check_cancellation()
            self._update_progress(10, "Validating backup file...")
            
            # Verify backup if requested
            if config.get('verification', True):
                self._update_progress(20, "Verifying backup integrity...")
                if not self._verify_backup_integrity(backup_path):
                    raise RestoreError("Backup integrity verification failed")
            
            self._check_cancellation()
            
            # Create backup of current database if requested
            current_backup_path = None
            if config.get('backup_current', True):
                self._update_progress(30, "Creating backup of current database...")
                current_backup_path = self._backup_current_database()
            
            self._check_cancellation()
            self._update_progress(50, "Preparing for restore...")
            
            # Extract backup if compressed
            extracted_backup_path = self._extract_backup_if_compressed(backup_path)
            
            try:
                # Perform the restore
                self._update_progress(60, "Restoring database...")
                success = self._perform_database_restore(extracted_backup_path, config)
                
                if success:
                    self._update_progress(90, "Finalizing restore...")
                    self._record_restore_history(backup_path, config, True)
                    self._update_progress(100, "Restore completed successfully")
                    return True
                else:
                    raise RestoreError("Database restore operation failed")
                    
            finally:
                # Clean up extracted backup if it was compressed
                if extracted_backup_path != backup_path and os.path.exists(extracted_backup_path):
                    os.remove(extracted_backup_path)
                    
        except RestoreError:
            self._record_restore_history(backup_path, config, False)
            raise
        except Exception as e:
            self._record_restore_history(backup_path, config, False)
            raise RestoreError(f"Unexpected error during restore: {e}")
    
    def _validate_backup_file(self, backup_path: str) -> bool:
        """Validate that the backup file exists and is accessible."""
        print(f'[DEBUG] RestoreManager._validate_backup_file called for {backup_path}')
        
        if not os.path.exists(backup_path):
            print(f'[ERROR] Backup file does not exist: {backup_path}')
            return False
        
        if not os.path.isfile(backup_path):
            print(f'[ERROR] Backup path is not a file: {backup_path}')
            return False
        
        if os.path.getsize(backup_path) == 0:
            print(f'[ERROR] Backup file is empty: {backup_path}')
            return False
        
        return True
    
    def _verify_backup_integrity(self, backup_path: str) -> bool:
        """Verify backup file integrity using checksum."""
        print(f'[DEBUG] RestoreManager._verify_backup_integrity called')
        
        try:
            # Calculate current checksum
            current_checksum = self._calculate_file_checksum(backup_path)
            
            # Try to get stored checksum from backup history
            stored_checksum = self._get_backup_checksum_from_history(backup_path)
            
            if stored_checksum:
                if current_checksum == stored_checksum:
                    print('[DEBUG] Backup integrity verified - checksums match')
                    return True
                else:
                    print(f'[ERROR] Backup integrity check failed - checksum mismatch')
                    print(f'[ERROR] Expected: {stored_checksum}, Got: {current_checksum}')
                    return False
            else:
                # If no stored checksum, try to verify by opening the database
                print('[DEBUG] No stored checksum found, attempting database verification')
                return self._verify_backup_database_structure(backup_path)
                
        except Exception as e:
            print(f'[ERROR] Backup integrity verification failed: {e}')
            return False
    
    def _calculate_file_checksum(self, file_path: str) -> str:
        """Calculate SHA-256 checksum of file."""
        print(f'[DEBUG] RestoreManager._calculate_file_checksum called')
        
        sha256_hash = hashlib.sha256()
        
        # Handle compressed files
        if file_path.endswith('.gz'):
            with gzip.open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
        else:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    def _get_backup_checksum_from_history(self, backup_path: str) -> Optional[str]:
        """Get stored checksum for backup from history table."""
        print(f'[DEBUG] RestoreManager._get_backup_checksum_from_history called')
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT checksum FROM backup_history 
                    WHERE backup_path = ? 
                    ORDER BY created_date DESC 
                    LIMIT 1
                """, (backup_path,))
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            print(f'[ERROR] Failed to get backup checksum from history: {e}')
            return None
    
    def _verify_backup_database_structure(self, backup_path: str) -> bool:
        """Verify backup by checking database structure."""
        print(f'[DEBUG] RestoreManager._verify_backup_database_structure called')
        
        try:
            # Extract if compressed
            temp_path = None
            db_path = backup_path
            
            if backup_path.endswith('.gz'):
                temp_path = backup_path[:-3] + '_temp.db'
                with gzip.open(backup_path, 'rb') as f_in:
                    with open(temp_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                db_path = temp_path
            
            try:
                # Try to connect and check basic structure
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check if essential tables exist
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name IN ('clips_tbl', 'more_bus_tbl', 'more_comp_tbl')
                """)
                
                tables = [row[0] for row in cursor.fetchall()]
                expected_tables = ['clips_tbl', 'more_bus_tbl', 'more_comp_tbl']
                
                conn.close()
                
                # Check if all essential tables are present
                missing_tables = set(expected_tables) - set(tables)
                if missing_tables:
                    print(f'[ERROR] Missing essential tables in backup: {missing_tables}')
                    return False
                
                print('[DEBUG] Backup database structure verification passed')
                return True
                
            finally:
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                    
        except Exception as e:
            print(f'[ERROR] Backup database structure verification failed: {e}')
            return False
