#!/usr/bin/env python3
import sqlite3

conn = sqlite3.connect('source/DB/clipsmore_db.db')
cursor = conn.cursor()

# First check table structure
cursor.execute("PRAGMA table_info(clips_tbl)")
columns = cursor.fetchall()
print("Clips table columns:")
for col in columns:
    print(f"  {col[1]} ({col[2]})")

print("\nAll clips in database:")
cursor.execute('SELECT clip_id, clip, timestamp FROM clips_tbl ORDER BY clip_id DESC LIMIT 10')
results = cursor.fetchall()

for row in results:
    print(f'  Clip {row[0]}: "{row[1][:50]}..." (created: {row[2]})')

print(f"\nTotal clips: {len(results)}")

# Check for the test content we copied
cursor.execute('SELECT clip_id, clip FROM clips_tbl WHERE clip LIKE "%test clipboard content%"')
test_clips = cursor.fetchall()

if test_clips:
    print(f"\nFound test clips:")
    for row in test_clips:
        print(f'  Clip {row[0]}: "{row[1]}"')
else:
    print("\nNo test clips found")

conn.close()
