# 📚 ClipsMore User Guide

## 🌟 Overview
ClipsMore is an advanced clipboard management application that allows you to organize your clipboard content with hierarchical business cases and components. This guide will help you get started and make the most of ClipsMore's powerful features! 🚀

## 📋 Table of Contents
1. [🚀 Getting Started](#getting-started)
2. [📋 Enhanced Clips Tab](#enhanced-clips-tab)
3. [🌳 More Tab - Business Organization](#more-tab---business-organization)
4. [🖱️ Drag & Drop Operations](#drag--drop-operations)
5. [⚡ Advanced Features](#advanced-features)
6. [🔧 Troubleshooting](#troubleshooting)

## 🚀 Getting Started

### 📦 Installation
1. 🐍 Ensure Python 3.8+ is installed on your system
2. 📥 Download or clone the ClipsMore application
3. 📁 Navigate to the source directory
4. ▶️ Run the application: `python main.py`

### 🎉 First Launch
When you first launch ClipsMore, you'll see three main tabs:
- **📋 Clips**: Enhanced clipboard management with individual clip controls
- **🌳 More**: Business case and component organization
- **📚 About**: Application information and documentation

### 🎨 Theme Support
ClipsMore supports both light and dark themes. Click the "🌓 Toggle Theme" button in any tab to switch between themes.

## 📋 Enhanced Clips Tab

### 🔍 Overview
The Enhanced Clips tab provides individual management controls for each clipboard item, making it easy to organize and assign clips to business contexts. 🎯

### ✨ Features

#### 🧩 Individual Clip Widgets
Each clip is displayed in its own widget containing:
- **👀 Content Preview**: First 100 characters of the clip content
- **📄 Copy Button**: Click to copy the clip content to your OS clipboard
- **🏷️ Alias Field**: Editable alias with real-time validation
- **📋 Assignment Dropdown**: Select business case or component for assignment
- **🎯 Assign Button**: Create the assignment relationship
- **🗑️ Delete Button**: Remove individual clips

#### 🧠 Auto-Generated Aliases
ClipsMore automatically generates intelligent aliases for your clips based on content analysis:

**🌐 URL Content**: Extracts domain names
- `https://github.com/user/repo` → `github`
- `https://api.example.com/users` → `example`

**📁 File Names**: Extracts meaningful file names
- `document.pdf` → `document`
- `data_analysis.xlsx` → `data_analysis`

**💻 Code Content**: Identifies functions and variables
- `def calculate_total():` → `calculate_total`
- `api_key = "secret"` → `api_key`

**🔧 Technical Terms**: Recognizes programming terms
- `SQL database query` → `sql`
- `JSON response format` → `json`

**📝 Meaningful Words**: Extracts important words
- `Database connection settings` → `database_connection`
- `User authentication system` → `user_authentication`

#### ✅ Real-Time Alias Validation
The alias field provides visual feedback:
- **✓🟢 Green**: Valid and unique alias
- **⚠️🟠 Orange**: Valid but already exists (duplicate)
- **✗🔴 Red**: Invalid format
- **❓⚪ Gray**: Unknown status

#### 🎯 Assignment Process
1. **🧠 Auto-Generated Alias**: Each clip gets an intelligent alias automatically
2. **✏️ Edit Alias**: Modify the alias if needed (validation updates in real-time)
3. **📋 Select Assignment**: Choose from dropdown of business cases and components
4. **🎯 Assign**: Click "Assign" to create the relationship
5. **🔄 Conflict Resolution**: If alias conflicts occur, alternative suggestions are provided

#### ⚡ Bulk Operations
- **🧹 Clear All**: Remove all clips at once (with confirmation dialog)

## 🌳 More Tab - Business Organization

### 🔍 Overview
The More tab allows you to create and manage hierarchical business cases and components, and view assigned clips as interactive buttons. 🏢

### 🏢 Business Case Management
1. **➕ Add Business Case**: Enter name and click "Add Business Case"
2. **👀 View in Tree**: Business cases appear as top-level items
3. **🔽🔼 Expand/Collapse**: Click to show/hide components and clip buttons

### 🧩 Component Management
1. **🖱️ Select Business Case**: Click on a business case in the tree
2. **➕ Add Component**: Enter component name and click "CUD Component"
3. **🗑️ Delete Component**: Select component, type "delete", click "CUD Component"

### 📎 Clip Buttons in Tree
Assigned clips appear as clickable buttons (📎) next to their business cases or components:
- **🖱️ Double-Click**: Copy clip content to clipboard
- **🖱️ Drag & Drop**: Move or copy clips between business contexts
- **👀 Visual Organization**: See all your clips organized by business context

### 🌳 Tree Structure Example
```
📁 Development
  📁 Frontend
    📎 react_component
    📎 css_styles
  📁 Backend
    📎 api_endpoint
    📎 database_query
  📎 general_dev_notes

📁 Documentation
  📁 User Guide
    📎 user_instructions
  📎 api_documentation
```

## 🖱️ Drag & Drop Operations

### 🔍 Overview
ClipsMore supports intuitive drag and drop operations to move or copy clip assignments between business contexts. ✨

### 🎯 How to Use Drag & Drop
1. **🚀 Initiate Drag**: Click and hold on a clip button (📎) in the More tab tree
2. **🎯 Drag to Target**: Drag to a business case or component
3. **📋 Context Menu**: Release to see options:
   - **🔄 Move**: Transfer the clip assignment to the new location
   - **📄 Copy**: Create a duplicate assignment in the new location
   - **❌ Cancel**: Abort the operation

### 👀 Visual Feedback
- **🖱️ Cursor Change**: Cursor changes to indicate dragging mode
- **✨ Target Highlighting**: Valid drop targets are highlighted
- **📋 Context Menu**: Clear options for Move/Copy/Cancel

### 🔄 Move vs Copy
- **🔄 Move**: Clip assignment changes location (original assignment is removed)
- **📄 Copy**: Creates a new assignment (original remains, new alias auto-generated)

## ⚡ Advanced Features

### 🔄 Alias Conflict Resolution
When assigning clips, if an alias already exists:
1. **🔍 Automatic Detection**: System detects the conflict
2. **💡 Alternative Suggestions**: Provides 3 alternative aliases
3. **👤 User Choice**: Accept suggestion or manually edit
4. **✨ Unique Enforcement**: Ensures all aliases remain unique

### 🔍 Search and Filter
- **🌳 Tree Search**: Use the search box in More tab to filter business cases
- **⚡ Quick Navigation**: Type to quickly find specific business contexts

### 🗄️ Database Integration
- **💾 Automatic Backup**: Database is automatically backed up before migrations
- **🔗 Foreign Key Integrity**: Relationships are maintained automatically
- **🛡️ Transaction Safety**: All operations are atomic and safe

### ⚡ Performance Optimization
- **📦 Lazy Loading**: Large clip lists load incrementally
- **🏊 Connection Pooling**: Efficient database operations
- **🔄 Real-time Updates**: UI updates immediately reflect changes

## ⌨️ Keyboard Shortcuts

### 🌐 General
- **🎨 Ctrl+T**: Toggle theme (light/dark)
- **🔄 F5**: Refresh current tab

### 📋 Clips Tab
- **✅ Enter**: Confirm alias edit
- **🗑️ Delete**: Delete selected clip (with confirmation)
- **📋 Ctrl+A**: Select all clips

### 🌳 More Tab
- **✅ Enter**: Add business case or component
- **🗑️ Delete**: Delete selected item
- **✏️ F2**: Rename selected item

## 💡 Tips and Best Practices

### 📋 Organizing Your Clips
1. **📝 Use Descriptive Aliases**: Make aliases meaningful and searchable
2. **🌳 Hierarchical Organization**: Use business cases for major categories, components for subcategories
3. **🧹 Regular Cleanup**: Periodically review and delete unused clips
4. **📏 Consistent Naming**: Develop naming conventions for your aliases

### ⚡ Workflow Optimization
1. **📦 Batch Assignment**: Assign multiple related clips at once
2. **🖱️ Drag & Drop**: Use drag and drop for quick reorganization
3. **🔍 Search First**: Use search to find existing assignments before creating new ones
4. **🎨 Theme Preference**: Choose the theme that works best for your environment

### 💾 Data Management
1. **💾 Regular Backups**: The system creates automatic backups, but consider manual exports
2. **✨ Alias Uniqueness**: Keep aliases unique and meaningful
3. **🧹 Relationship Cleanup**: Remove unused business cases and components periodically

## 🔧 Troubleshooting

### ⚠️ Common Issues

#### 📋 Clips Not Appearing
- **🗄️ Check Database**: Ensure database file exists and is accessible
- **🔄 Restart Application**: Close and reopen ClipsMore
- **🔒 Check Permissions**: Verify file system permissions

#### ❌ Assignment Failures
- **🔄 Alias Conflicts**: Use suggested alternatives or create unique aliases
- **❌ Invalid Characters**: Aliases must start with letters and contain only letters, numbers, underscores, and hyphens
- **🏢 Missing Business Case**: Ensure target business case exists

#### 🖱️ Drag & Drop Not Working
- **🖱️ Click and Hold**: Ensure you click and hold before dragging
- **🎯 Valid Targets**: Only business cases and components are valid drop targets
- **📋 Context Menu**: Wait for context menu to appear after dropping

#### ⚡ Performance Issues
- **📊 Large Datasets**: Consider clearing old clips if you have thousands
- **💾 Database Size**: Check database file size and consider cleanup
- **🧠 System Resources**: Ensure adequate system memory

### 🚨 Error Messages

#### ❌ "Alias already exists"
- **✅ Solution**: Use the suggested alternatives or create a unique alias
- **🛡️ Prevention**: Check existing aliases before assignment

#### ❌ "Invalid alias format"
- **✅ Solution**: Ensure alias starts with a letter and contains only valid characters
- **📏 Valid Format**: Letters, numbers, underscores, and hyphens only

#### ❌ "Database connection failed"
- **✅ Solution**: Check database file permissions and location
- **🔄 Recovery**: Restart application or restore from backup

### 🆘 Getting Help
1. **🔍 Debug Mode**: Check console output for detailed error messages
2. **📄 Log Files**: Review application logs for troubleshooting information
3. **💾 Database Backup**: Restore from automatic backup if data issues occur
4. **🔄 Reset Settings**: Delete configuration files to reset to defaults

## ⚙️ Advanced Configuration

### 💾 Database Location
The database is stored in `source/DB/clipsmore_db.db`. You can:
- **💾 Backup**: Copy this file to create manual backups
- **🔄 Restore**: Replace with backup to restore previous state
- **📁 Move**: Change location by updating configuration

### 🎨 Theme Customization
Themes are defined in the UI manager. You can:
- **🎨 Modify Colors**: Edit theme color definitions
- **✨ Create Custom**: Add new theme configurations
- **💻 System Integration**: Use system theme detection

### ⚡ Performance Tuning
For large datasets:
- **🏊 Connection Pool Size**: Adjust database connection pool
- **📦 Lazy Loading Threshold**: Modify clip loading batch size
- **⚡ Cache Settings**: Configure UI update frequency

---

**📋 Version**: 2.0
**📅 Last Updated**: 2025-06-06
**🆘 For Technical Support**: See technical documentation in `docs/technical/` 📚
