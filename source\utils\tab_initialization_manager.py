"""
TabInitializationManager - Handles tab content initialization

This manager centralizes the initialization logic for all tab content,
including clips tab, more tab, and about tab setup.
"""

import tkinter as tk
from typing import Dict, Any, Optional, Callable


class TabInitializationManager:
    """
    Manages tab content initialization and setup.
    
    This class handles the initialization of all tab content,
    coordinating with various managers to set up each tab properly.
    """
    
    def __init__(self, theme_manager, database_manager, validation_manager):
        """
        Initialize TabInitializationManager.
        
        Args:
            theme_manager: ThemeManager instance
            database_manager: DatabaseManager instance
            validation_manager: ValidationManager instance
        """
        print('[DEBUG] TabInitializationManager.__init__ called')
        self.theme_manager = theme_manager
        self.database_manager = database_manager
        self.validation_manager = validation_manager
        self.initialized_managers = {}
        
    def initialize_clips_tab(self, clips_tab, clip_manager_class, ui_manager_ref):
        """
        Initialize the clips tab with ClipManager.
        
        Args:
            clips_tab: Clips tab widget
            clip_manager_class: ClipManager class
            ui_manager_ref: Reference to UIManager instance
            
        Returns:
            ClipManager instance
        """
        print('[DEBUG] TabInitializationManager.initialize_clips_tab called')
        
        try:
            # Initialize ClipManager
            clip_manager = clip_manager_class(
                parent=clips_tab,
                theme_manager=self.theme_manager,
                database_manager=self.database_manager,
                validation_manager=self.validation_manager,
                ui_manager=ui_manager_ref
            )
            
            # Create clips interface
            clips_interface = clip_manager.create_clips_interface()
            clips_interface.pack(fill='both', expand=True)

            # Load clips
            clip_manager.load_clips()
            
            self.initialized_managers['clip_manager'] = clip_manager
            print('[DEBUG] Clips tab initialized successfully')
            return clip_manager
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize clips tab: {e}')
            raise
            

        
    def initialize_about_tab(self, about_tab, documentation_manager_class):
        """
        Initialize the about tab with DocumentationManager.
        
        Args:
            about_tab: About tab widget
            documentation_manager_class: DocumentationManager class
            
        Returns:
            DocumentationManager instance
        """
        print('[DEBUG] TabInitializationManager.initialize_about_tab called')
        
        try:
            # Initialize DocumentationManager
            documentation_manager = documentation_manager_class(about_tab, self.theme_manager)
            
            # Create documentation interface
            doc_interface = documentation_manager.create_documentation_interface()
            if doc_interface:
                doc_interface.pack(fill='both', expand=True)
                print('[DEBUG] DocumentationManager interface created successfully')
            else:
                print('[WARNING] Failed to create DocumentationManager interface')
                
            self.initialized_managers['documentation_manager'] = documentation_manager
            print('[DEBUG] About tab initialized successfully')
            return documentation_manager
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize about tab: {e}')
            raise
            
    def initialize_more_tab_basic_ui(self, more_tab):
        """
        Initialize basic UI elements for the more tab.
        
        Args:
            more_tab: More tab widget
            
        Returns:
            Dictionary containing UI elements
        """
        print('[DEBUG] TabInitializationManager.initialize_more_tab_basic_ui called')
        
        try:
            colors = self.theme_manager.get_theme_colors()
            ui_elements = {}
            
            # Create main label
            label = tk.Label(
                more_tab, 
                text="Business Case Manager", 
                bg=colors['bg_color'], 
                fg=colors['fg_color']
            )
            label.pack(pady=20)
            
            # Create inline frame for business case/component UI
            inline_frame = tk.Frame(more_tab, bg=colors['bg_color'])
            inline_frame.pack(pady=5)
            ui_elements['inline_frame'] = inline_frame
            
            # Create business case entry elements
            self._create_business_case_entry(inline_frame, colors, ui_elements)
            
            # Create component entry elements
            self._create_component_entry(inline_frame, colors, ui_elements)

            # Create error label
            more_error_label = tk.Label(more_tab, text="", fg="red", bg=colors['bg_color'])
            more_error_label.pack()
            ui_elements['more_error_label'] = more_error_label
            
            print('[DEBUG] More tab basic UI initialized successfully')
            return ui_elements
            
        except Exception as e:
            print(f'[ERROR] Failed to initialize more tab basic UI: {e}')
            raise
            
    def _create_business_case_entry(self, parent, colors, ui_elements):
        """Create business case entry elements."""
        print('[DEBUG] TabInitializationManager._create_business_case_entry called')
        
        # Business case label
        tk.Label(
            parent, 
            text="Business Case:", 
            bg=colors['bg_color'], 
            fg=colors['fg_color']
        ).pack(side=tk.LEFT, padx=2)
        
        # Business case entry
        bus_entry_var = tk.StringVar()
        bus_entry = tk.Entry(
            parent, 
            textvariable=bus_entry_var, 
            width=22,
            bg=colors['entry_bg'], 
            fg=colors['entry_fg'], 
            insertbackground=colors['entry_fg']
        )
        bus_entry.pack(side=tk.LEFT, padx=2)
        
        # Add business case button
        tk.Button(
            parent, 
            text="Add Business Case", 
            command=lambda: self._placeholder_callback("add_business_case"),
            bg=colors['button_bg'], 
            fg=colors['button_fg'], 
            activebackground=colors['tree_select'],
            activeforeground=colors['fg_color']
        ).pack(side=tk.LEFT, padx=5)
        
        ui_elements['bus_entry_var'] = bus_entry_var
        ui_elements['bus_entry'] = bus_entry
        
    def _create_component_entry(self, parent, colors, ui_elements):
        """Create component entry elements."""
        print('[DEBUG] TabInitializationManager._create_component_entry called')
        
        # Component label
        tk.Label(
            parent, 
            text="Component:", 
            bg=colors['bg_color'], 
            fg=colors['fg_color']
        ).pack(side=tk.LEFT, padx=2)
        
        # Component entry
        comp_entry_var = tk.StringVar()
        comp_entry = tk.Entry(
            parent, 
            textvariable=comp_entry_var, 
            width=22,
            bg=colors['entry_bg'], 
            fg=colors['entry_fg'], 
            insertbackground=colors['entry_fg']
        )
        comp_entry.pack(side=tk.LEFT, padx=2)
        
        # CUD Component button
        add_component_btn = tk.Button(
            parent, 
            text="CUD Component", 
            command=lambda: self._placeholder_callback("cud_component"),
            bg=colors['button_bg'], 
            fg=colors['button_fg'], 
            activebackground=colors['tree_select'],
            activeforeground=colors['fg_color']
        )
        add_component_btn.pack(side=tk.LEFT, padx=5)
        
        ui_elements['comp_entry_var'] = comp_entry_var
        ui_elements['comp_entry'] = comp_entry
        ui_elements['add_component_btn'] = add_component_btn
        

        
    def get_initialized_manager(self, manager_name: str) -> Optional[Any]:
        """
        Get an initialized manager by name.
        
        Args:
            manager_name: Name of the manager
            
        Returns:
            Manager instance or None if not found
        """
        return self.initialized_managers.get(manager_name)
