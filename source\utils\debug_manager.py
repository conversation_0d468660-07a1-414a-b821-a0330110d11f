"""
DebugManager - Debug and testing utilities for ClipsMore application

This module handles debug functions and test data loading for development
and testing purposes. These functions should be removed before production.

Author: ClipsMore Development Team
Date: 2025-06-16
"""

from tkinter import messagebox


class DebugManager:
    """
    Manages debug functions and test data loading for the ClipsMore application.
    
    Provides utilities for loading test clips, business cases, and components
    for development and testing purposes.
    """
    
    def __init__(self, database_manager, clip_manager, tree_manager):
        """
        Initialize DebugManager with manager references.
        
        Args:
            database_manager: DatabaseManager instance
            clip_manager: ClipManager instance  
            tree_manager: TreeManager instance
        """
        print('[DEBUG] DebugManager.__init__ called')
        
        self.database_manager = database_manager
        self.clip_manager = clip_manager
        self.tree_manager = tree_manager
        self.notification_manager = None  # Will be set by UIManager if available

        print('[DEBUG] DebugManager initialized successfully')

    def set_notification_manager(self, notification_manager):
        """Set the notification manager for this debug manager."""
        print('[DEBUG] DebugManager.set_notification_manager called')
        self.notification_manager = notification_manager
    
    def load_test_clips(self):
        """Load cybersecurity test data for development and testing"""
        print('[DEBUG] DebugManager.load_test_clips called')

        try:
            # Confirm loading cybersecurity test data
            result = messagebox.askyesno("DEBUG: Load Cybersecurity Data",
                                       "This will add comprehensive cybersecurity clips, business cases, components, and transactions to the database.\n\n"
                                       "Continue?")

            if not result:
                return

            # Load comprehensive cybersecurity data
            self._load_cybersecurity_data()

        except Exception as e:
            print(f'[ERROR] Failed to load cybersecurity data: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to load cybersecurity data: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to load cybersecurity data: {str(e)}")

    def _load_cybersecurity_data(self):
        """Load comprehensive cybersecurity data including clips, business cases, components, and transactions"""
        print('[DEBUG] DebugManager._load_cybersecurity_data called')

        clips_ops = self.database_manager.get_clips_operations()
        more_ops = self.database_manager.get_more_operations()
        enhanced_ops = self.database_manager.get_enhanced_operations()

        created_clips = 0
        created_business_cases = 0
        created_components = 0
        created_transactions = 0

        try:
            # Cybersecurity clips data
            cybersecurity_clips = [
                {
                    'clip': "Cybercriminals are getting smarter. Are you?\nA 76-year-old retired lawyer lost his entire life savings to a sophisticated phishing attack that started with a single email.",
                    'alias': 'cybercriminals_intro'
                },
                {
                    'clip': "Imposter scams: They can pose as government agents by phone or email, claiming your accounts are compromised and demanding immediate payment to avoid arrest.",
                    'alias': 'imposter_scams'
                },
                {
                    'clip': "Romance scams: These cybercriminals play the long game— building trust over weeks by phone, text, or social media before asking for money for emergencies.",
                    'alias': 'romance_scams'
                },
                {
                    'clip': "Call-center scams: If you get a call or receive a computer pop up claiming your computer is experiencing problems, hang up immediately. These are fake tech support scams.",
                    'alias': 'tech_support_scams'
                },
                {
                    'clip': "Phishing emails often contain urgent language like 'Your account will be closed' or 'Verify immediately' to pressure you into clicking malicious links.",
                    'alias': 'phishing_emails'
                },
                {
                    'clip': "Two-factor authentication (2FA) adds an extra layer of security by requiring a second form of verification beyond just your password.",
                    'alias': 'two_factor_auth'
                },
                {
                    'clip': "Password managers generate and store unique, complex passwords for each of your accounts, significantly improving your security posture.",
                    'alias': 'password_managers'
                },
                {
                    'clip': "Ransomware attacks encrypt your files and demand payment for the decryption key. Regular backups are your best defense against these attacks.",
                    'alias': 'ransomware_defense'
                },
                {
                    'clip': "Social engineering attacks manipulate human psychology rather than technical vulnerabilities to gain unauthorized access to systems or information.",
                    'alias': 'social_engineering'
                },
                {
                    'clip': "Business Email Compromise (BEC) attacks target companies by impersonating executives or vendors to trick employees into transferring money or sensitive data.",
                    'alias': 'bec_attacks'
                },
                {
                    'clip': "Zero-day vulnerabilities are security flaws unknown to software vendors, making them particularly dangerous as no patches exist yet.",
                    'alias': 'zero_day_vulns'
                },
                {
                    'clip': "VPN (Virtual Private Network) creates a secure, encrypted connection between your device and the internet, protecting your data from eavesdropping.",
                    'alias': 'vpn_security'
                },
                {
                    'clip': "Incident response plans outline the steps to take when a security breach occurs, helping minimize damage and recovery time.",
                    'alias': 'incident_response'
                },
                {
                    'clip': "Security awareness training educates employees about cyber threats and best practices, turning them into the first line of defense.",
                    'alias': 'security_training'
                },
                {
                    'clip': "Data encryption converts readable information into coded format that can only be deciphered with the correct decryption key.",
                    'alias': 'data_encryption'
                }
            ]

            # Create clips
            for clip_data in cybersecurity_clips:
                try:
                    clip_id = clips_ops.create_clip(clip_data)
                    print(f'[DEBUG] Created cybersecurity clip {clip_id}: {clip_data["alias"]}')
                    created_clips += 1
                except Exception as e:
                    print(f'[ERROR] Failed to create clip {clip_data["alias"]}: {e}')

            # Cybersecurity business cases
            business_cases = [
                "Cyber Security",
                "Network Security",
                "Data Protection",
                "Incident Response",
                "Security Training"
            ]

            # Create business cases
            business_case_ids = {}
            for bc_name in business_cases:
                try:
                    bc_id, _ = more_ops.create_more(bc_name)
                    business_case_ids[bc_name] = bc_id
                    print(f'[DEBUG] Created business case {bc_id}: {bc_name}')
                    created_business_cases += 1
                except Exception as e:
                    print(f'[ERROR] Failed to create business case {bc_name}: {e}')

            # Cybersecurity components by business case
            components_data = [
                # Cyber Security components
                ("Cyber Security", "Imposter scams"),
                ("Cyber Security", "Romance scams"),
                ("Cyber Security", "Call-center scams"),
                ("Cyber Security", "Phishing attacks"),
                ("Cyber Security", "Social engineering"),

                # Network Security components
                ("Network Security", "VPN security"),
                ("Network Security", "Firewall configuration"),
                ("Network Security", "Network monitoring"),

                # Data Protection components
                ("Data Protection", "Data encryption"),
                ("Data Protection", "Backup strategies"),
                ("Data Protection", "Access controls"),

                # Incident Response components
                ("Incident Response", "Response planning"),
                ("Incident Response", "Forensic analysis"),
                ("Incident Response", "Recovery procedures"),

                # Security Training components
                ("Security Training", "Awareness training"),
                ("Security Training", "Phishing simulations"),
                ("Security Training", "Security policies")
            ]

            # Create components and store component IDs
            component_ids = {}
            for bc_name, comp_name in components_data:
                try:
                    if bc_name in business_case_ids:
                        comp_id = more_ops.add_component(business_case_ids[bc_name], comp_name)
                        component_ids[f"{bc_name}:{comp_name}"] = comp_id
                        print(f'[DEBUG] Created component {comp_id}: {comp_name} under {bc_name}')
                        created_components += 1
                except Exception as e:
                    print(f'[ERROR] Failed to create component {comp_name}: {e}')

            # Create cybersecurity transactions (assignments)
            transactions_data = [
                # Cyber Security transactions
                (1, "cybercriminals_intro", "Cyber Security", None),
                (2, "imposter_scams", "Cyber Security", "Imposter scams"),
                (3, "romance_scams", "Cyber Security", "Romance scams"),
                (4, "tech_support_scams", "Cyber Security", "Call-center scams"),
                (5, "phishing_emails", "Cyber Security", "Phishing attacks"),
                (9, "social_engineering", "Cyber Security", "Social engineering"),

                # Network Security transactions
                (12, "vpn_security", "Network Security", "VPN security"),

                # Data Protection transactions
                (6, "two_factor_auth", "Data Protection", "Access controls"),
                (7, "password_managers", "Data Protection", "Access controls"),
                (8, "ransomware_defense", "Data Protection", "Backup strategies"),
                (15, "data_encryption", "Data Protection", "Data encryption"),

                # Incident Response transactions
                (10, "bec_attacks", "Incident Response", "Response planning"),
                (11, "zero_day_vulns", "Incident Response", "Forensic analysis"),
                (13, "incident_response", "Incident Response", "Response planning"),

                # Security Training transactions
                (14, "security_training", "Security Training", "Awareness training"),
            ]

            # Create transactions
            for clip_id, alias, bc_name, comp_name in transactions_data:
                try:
                    if bc_name in business_case_ids:
                        bus_id = business_case_ids[bc_name]
                        comp_id = None

                        if comp_name:
                            comp_key = f"{bc_name}:{comp_name}"
                            comp_id = component_ids.get(comp_key)

                        transaction_id = enhanced_ops.create_assignment(
                            clip_id=clip_id,
                            more_bus_id=bus_id,
                            more_comp_id=comp_id,
                            alias=alias
                        )
                        print(f'[DEBUG] Created transaction {transaction_id}: {alias} -> {bc_name}:{comp_name or "None"}')
                        created_transactions += 1
                except Exception as e:
                    print(f'[ERROR] Failed to create transaction {alias}: {e}')

            # Refresh displays
            if self.tree_manager:
                self.tree_manager.refresh_tree()
            if self.clip_manager:
                self.clip_manager.load_clips()

            # Show success message
            total_created = created_clips + created_business_cases + created_components + created_transactions
            success_message = (f"Successfully loaded cybersecurity data:\n"
                             f"• {created_clips} clips\n"
                             f"• {created_business_cases} business cases\n"
                             f"• {created_components} components\n"
                             f"• {created_transactions} transactions\n"
                             f"Total: {total_created} items")

            if self.notification_manager:
                self.notification_manager.show_success(success_message)
            else:
                messagebox.showinfo("DEBUG: Cybersecurity Data Loaded", success_message)

        except Exception as e:
            print(f'[ERROR] Failed to load cybersecurity data: {e}')
            raise
    

    
    def clear_all_clips(self):
        """Clear all clips with confirmation"""
        print('[DEBUG] DebugManager.clear_all_clips called')
        
        if self.clip_manager:
            self.clip_manager.clear_all_clips()
        else:
            print('[WARNING] ClipManager not available')
    
    def clear_all_more_data(self):
        """Clear all business cases, components, and assignments with confirmation"""
        print('[DEBUG] DebugManager.clear_all_more_data called')
        
        try:
            # Confirm deletion - keep messagebox for user confirmation
            result = messagebox.askyesno("Confirm Clear All Data",
                                       "Are you sure you want to delete ALL business cases, components, and assignments?\n"
                                       "This will remove all organizational data but keep your clips.\n"
                                       "This action cannot be undone.")
            
            if result:
                more_ops = self.database_manager.get_more_operations()
                enhanced_ops = self.database_manager.get_enhanced_operations()
                
                # Clear all assignments first (to avoid foreign key issues)
                enhanced_ops.clear_all_assignments()
                
                # Clear all components
                more_ops.clear_all_components()
                
                # Clear all business cases
                more_ops.clear_all_business_cases()
                
                print('[DEBUG] Cleared all More tab data')
                
                # Refresh displays
                if self.tree_manager:
                    self.tree_manager.refresh_tree()
                if self.clip_manager:
                    self.clip_manager.load_clips()  # Refresh clips to update assignment dropdowns
                
        except Exception as e:
            print(f'[ERROR] Failed to clear all More data: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to clear data: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to clear data: {str(e)}")
    
    def show_debug_info(self):
        """Show debug information about the current state"""
        print('[DEBUG] DebugManager.show_debug_info called')
        
        try:
            info_lines = []
            info_lines.append("=== ClipsMore Debug Information ===")
            info_lines.append("")
            
            # Database info
            if self.database_manager:
                clips_ops = self.database_manager.get_clips_operations()
                more_ops = self.database_manager.get_more_operations()
                enhanced_ops = self.database_manager.get_enhanced_operations()
                
                # Count clips
                try:
                    clips = clips_ops.read_all_clips()
                    info_lines.append(f"Total Clips: {len(clips)}")
                except:
                    info_lines.append("Total Clips: Error reading")
                
                # Count business cases
                try:
                    business_cases = more_ops.read_all_business_cases()
                    info_lines.append(f"Total Business Cases: {len(business_cases)}")
                except:
                    info_lines.append("Total Business Cases: Error reading")
                
                # Count assignments
                try:
                    assignments = enhanced_ops.get_all_assignments()
                    info_lines.append(f"Total Assignments: {len(assignments)}")
                except:
                    info_lines.append("Total Assignments: Error reading")
            
            info_lines.append("")
            info_lines.append("=== Manager Status ===")
            info_lines.append(f"DatabaseManager: {'✓' if self.database_manager else '✗'}")
            info_lines.append(f"ClipManager: {'✓' if self.clip_manager else '✗'}")
            info_lines.append(f"TreeManager: {'✓' if self.tree_manager else '✗'}")
            
            debug_text = "\n".join(info_lines)

            # For debug info, keep messagebox since it's a large amount of text
            # that users need to read carefully - notifications are too brief
            messagebox.showinfo("Debug Information", debug_text)

            # Also print to console
            print(debug_text)

        except Exception as e:
            print(f'[ERROR] Failed to show debug info: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to get debug info: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to get debug info: {str(e)}")
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled"""
        # For now, always return True in development
        # In production, this could check environment variables or config files
        return True

    def test_notifications(self):
        """Test all notification types to verify the notification system works"""
        print('[DEBUG] DebugManager.test_notifications called')

        if not self.notification_manager:
            print('[WARNING] NotificationManager not available for testing')
            messagebox.showinfo("Test Notifications", "NotificationManager not available for testing")
            return

        try:
            # Test all notification types with a delay between them
            import threading
            import time

            def show_test_notifications():
                # Success notification
                self.notification_manager.show_success("✓ Success notification test - This is a success message!")
                time.sleep(0.5)

                # Info notification
                self.notification_manager.show_info("ℹ Info notification test - This is an informational message!")
                time.sleep(0.5)

                # Warning notification
                self.notification_manager.show_warning("⚠ Warning notification test - This is a warning message!")
                time.sleep(0.5)

                # Error notification
                self.notification_manager.show_error("✗ Error notification test - This is an error message!")
                time.sleep(0.5)

                # Final completion message
                self.notification_manager.show_info("🔔 Notification system test completed successfully!")

            # Run notifications in a separate thread to avoid blocking
            thread = threading.Thread(target=show_test_notifications)
            thread.daemon = True
            thread.start()

            print('[DEBUG] Notification tests started')

        except Exception as e:
            print(f'[ERROR] Failed to test notifications: {e}')
            if self.notification_manager:
                self.notification_manager.show_error(f"Failed to test notifications: {str(e)}")
            else:
                messagebox.showerror("Error", f"Failed to test notifications: {str(e)}")
