#!/usr/bin/env python3
"""
Test Directory Manager Functionality
Tests the enhanced directory detection with OneDrive migration support.
"""

import os
import sys
import unittest
import tempfile
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.directory_manager import DirectoryManager


class TestDirectoryManager(unittest.TestCase):
    """Test cases for the DirectoryManager."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestDirectoryManager.setUp called')
        
        # Create temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Initialize directory manager
        self.directory_manager = DirectoryManager()
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestDirectoryManager.tearDown called')
        
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_directory_manager_initialization(self):
        """Test directory manager initialization."""
        print('[DEBUG] test_directory_manager_initialization called')
        
        self.assertIsNotNone(self.directory_manager)
        self.assertIsNotNone(self.directory_manager.current_working_dir)
        self.assertIsNotNone(self.directory_manager.user_directories)
    
    def test_user_profile_detection(self):
        """Test user profile base detection."""
        print('[DEBUG] test_user_profile_detection called')
        
        base_path = self.directory_manager.detect_user_profile_base()
        self.assertIsInstance(base_path, Path)
        self.assertTrue(base_path.exists())
        
        print(f'[DEBUG] Detected user profile base: {base_path}')
    
    def test_user_directories_detection(self):
        """Test user directories detection."""
        print('[DEBUG] test_user_directories_detection called')
        
        directories = self.directory_manager.get_user_directories()
        
        # Check that all expected directories are present
        expected_keys = ['desktop', 'documents', 'downloads', 'profile']
        for key in expected_keys:
            self.assertIn(key, directories)
            self.assertIsInstance(directories[key], Path)
        
        print(f'[DEBUG] Detected directories: {directories}')
    
    def test_desktop_path_detection(self):
        """Test Desktop path detection."""
        print('[DEBUG] test_desktop_path_detection called')
        
        desktop_path = self.directory_manager.get_desktop_path()
        self.assertIsInstance(desktop_path, Path)
        
        # Desktop should exist (or fallback to profile)
        self.assertTrue(desktop_path.exists())
        
        print(f'[DEBUG] Desktop path: {desktop_path}')
    
    def test_documents_path_detection(self):
        """Test Documents path detection."""
        print('[DEBUG] test_documents_path_detection called')
        
        documents_path = self.directory_manager.get_documents_path()
        self.assertIsInstance(documents_path, Path)
        
        # Documents should exist (or fallback to profile)
        self.assertTrue(documents_path.exists())
        
        print(f'[DEBUG] Documents path: {documents_path}')
    
    def test_directory_validation(self):
        """Test directory validation functionality."""
        print('[DEBUG] test_directory_validation called')
        
        # Test valid directory
        valid_dir = self.temp_path / 'valid_test_dir'
        valid_dir.mkdir()
        
        self.assertTrue(self.directory_manager.validate_directory(str(valid_dir)))
        
        # Test invalid directory (non-existent)
        invalid_dir = self.temp_path / 'non_existent_dir'
        self.assertFalse(self.directory_manager.validate_directory(str(invalid_dir)))
        
        # Test file instead of directory
        test_file = self.temp_path / 'test_file.txt'
        test_file.touch()
        self.assertFalse(self.directory_manager.validate_directory(str(test_file)))
    
    def test_directory_creation(self):
        """Test directory creation functionality."""
        print('[DEBUG] test_directory_creation called')
        
        # Test creating new directory
        new_dir = self.temp_path / 'new_test_dir'
        self.assertTrue(self.directory_manager.create_directory_if_needed(str(new_dir)))
        self.assertTrue(new_dir.exists())
        
        # Test creating nested directory
        nested_dir = self.temp_path / 'nested' / 'test' / 'dir'
        self.assertTrue(self.directory_manager.create_directory_if_needed(str(nested_dir)))
        self.assertTrue(nested_dir.exists())
    
    def test_onedrive_path_detection(self):
        """Test OneDrive path detection logic."""
        print('[DEBUG] test_onedrive_path_detection called')
        
        # Create mock OneDrive structure
        mock_onedrive = self.temp_path / 'Users' / 'testuser' / 'OneDrive'
        mock_desktop = mock_onedrive / 'Desktop'
        mock_documents = mock_onedrive / 'Documents'
        
        mock_desktop.mkdir(parents=True)
        mock_documents.mkdir(parents=True)
        
        # Test finding desktop in OneDrive structure
        found_desktop = self.directory_manager.find_desktop_directory(mock_onedrive)
        self.assertEqual(found_desktop, mock_desktop)
        
        # Test finding documents in OneDrive structure
        found_documents = self.directory_manager.find_documents_directory(mock_onedrive)
        self.assertEqual(found_documents, mock_documents)
    
    def test_fallback_behavior(self):
        """Test fallback behavior when directories are not found."""
        print('[DEBUG] test_fallback_behavior called')
        
        # Test with non-existent base path
        non_existent_base = self.temp_path / 'non_existent'
        
        # Should fallback to base path when directories not found
        desktop_fallback = self.directory_manager.find_desktop_directory(non_existent_base)
        self.assertEqual(desktop_fallback, non_existent_base)
        
        documents_fallback = self.directory_manager.find_documents_directory(non_existent_base)
        self.assertEqual(documents_fallback, non_existent_base)
    
    def test_debug_info(self):
        """Test debug information generation."""
        print('[DEBUG] test_debug_info called')
        
        debug_info = self.directory_manager.get_debug_info()
        
        # Check that all expected debug fields are present
        expected_fields = [
            'current_working_dir',
            'detected_profile',
            'detected_desktop',
            'detected_documents',
            'detected_downloads',
            'home_fallback',
            'userprofile_env'
        ]
        
        for field in expected_fields:
            self.assertIn(field, debug_info)
            self.assertIsInstance(debug_info[field], str)
        
        print(f'[DEBUG] Debug info: {debug_info}')
    
    def test_path_consistency(self):
        """Test that detected paths are consistent across calls."""
        print('[DEBUG] test_path_consistency called')
        
        # Get directories multiple times
        dirs1 = self.directory_manager.get_user_directories()
        dirs2 = self.directory_manager.get_user_directories()
        
        # Should be consistent
        self.assertEqual(dirs1, dirs2)
        
        # Individual getters should be consistent
        desktop1 = self.directory_manager.get_desktop_path()
        desktop2 = self.directory_manager.get_desktop_path()
        self.assertEqual(desktop1, desktop2)


def run_directory_tests():
    """Run directory manager tests."""
    print("🧪 Running Directory Manager Tests")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestDirectoryManager)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 DIRECTORY MANAGER TEST SUMMARY")
    print("=" * 50)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")
    
    if failures == 0 and errors == 0:
        print("🎉 ALL DIRECTORY TESTS PASSED!")
        return True
    else:
        print("❌ SOME DIRECTORY TESTS FAILED!")
        return False


def print_current_directory_info():
    """Print current directory detection information for debugging."""
    print("\n" + "🔍 CURRENT DIRECTORY DETECTION INFO")
    print("=" * 50)
    
    try:
        dm = DirectoryManager()
        debug_info = dm.get_debug_info()
        
        for key, value in debug_info.items():
            print(f"{key}: {value}")
        
        print("\n📁 Detected User Directories:")
        directories = dm.get_user_directories()
        for name, path in directories.items():
            exists = "✅" if path.exists() else "❌"
            print(f"  {name}: {path} {exists}")
            
    except Exception as e:
        print(f"❌ Error getting directory info: {e}")


if __name__ == "__main__":
    print_current_directory_info()
    run_directory_tests()
